package com.saida.services.feign.system;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.dto.JumpDto;
import com.saida.services.common.dto.LoginToAdminByUser;
import com.saida.services.feign.system.fallback.IotSystemLogFallbackFactory;
import com.saida.services.system.pojo.Token;
import com.saida.services.system.sys.entity.OperatorLogEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(
        value = "iot-system-server",
        contextId = "feignIotSystemLogController",
        path = "/iot-system",
        fallbackFactory = IotSystemLogFallbackFactory.class)
public interface IFeignIotSystemLogController {

    /**
     * 保存日志
     */
    @PostMapping("/feign/iot-system/log/saveOperatorLog")
    void saveOperatorLog(OperatorLogEntity entity);

    @PostMapping("/feign/iot-system/auth/loginToAdmin")
    DtoResult<Token> loginToAdmin();


    @PostMapping("/feign/iot-system/auth/loginToAdminByUser")
    DtoResult<JumpDto> loginToAdminByUser(@RequestBody LoginToAdminByUser loginToAdminByUser);
}