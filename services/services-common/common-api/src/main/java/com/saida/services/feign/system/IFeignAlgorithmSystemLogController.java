package com.saida.services.feign.system;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.dto.JumpDto;
import com.saida.services.common.dto.LoginToAdminByUser;
import com.saida.services.feign.system.fallback.AlgSystemLogFallbackFactory;
import com.saida.services.system.pojo.Token;
import com.saida.services.system.sys.entity.OperatorLogEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


// 远程调用system微服务中的接口
@FeignClient(
        value = "converge-system-server",
        contextId = "feignAlgorithmSystemLogController",
        path = "/converge-system",
        fallbackFactory = AlgSystemLogFallbackFactory.class)
public interface IFeignAlgorithmSystemLogController {


    /**
     * 保存日志
     */
    @PostMapping("/feign/algorithm-system/log/saveOperatorLog")
    void saveOperatorLog(@RequestBody OperatorLogEntity entity);


    @PostMapping("/feign/algorithm-system/auth/loginToAdmin")
    DtoResult<Token> loginToAdmin();


    @PostMapping("/feign/algorithm-system/auth/loginToAdminByUser")
    DtoResult<JumpDto> loginToAdminByUser(@RequestBody LoginToAdminByUser loginToAdminByUser);
}
