package com.saida.services.feign.system;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.dto.JumpDto;
import com.saida.services.common.dto.LoginToAdminByUser;
import com.saida.services.feign.system.fallback.OpenSystemLogFallbackFactory;
import com.saida.services.system.pojo.Token;
import com.saida.services.system.sys.entity.AppCallLogEntity;
import com.saida.services.system.sys.entity.OperatorLogEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


// 远程调用system微服务中的接口
@FeignClient(
        value = "open-system-server",
        contextId = "feignOpenSystemLogController",
        path = "/open-system",
        fallbackFactory = OpenSystemLogFallbackFactory.class)
public interface IFeignOpenSystemLogController {


    /**
     * 保存日志
     */
    @PostMapping("/feign/open-system/log/saveOperatorLog")
    void saveOperatorLog(@RequestBody OperatorLogEntity entity);

    /**
     * 保存应用调用日志
     */
    @PostMapping("/feign/open-system/log/saveAppCallLog")
    void saveAppCallLog(@RequestBody AppCallLogEntity entity);


    @PostMapping("/feign/open-system/auth/loginToAdmin")
    DtoResult<Token> loginToAdmin();


    @PostMapping("/feign/open-system/auth/loginToAdminByUser")
    DtoResult<JumpDto> loginToAdminByUser(@RequestBody LoginToAdminByUser loginToAdminByUser);
}