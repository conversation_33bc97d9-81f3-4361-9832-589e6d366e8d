package com.saida.services.common.feign;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.constant.AuthConstants;
import com.saida.services.exception.BizRuntimeException;
import feign.FeignException;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Response;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.codec.ErrorDecoder;
import feign.optionals.OptionalDecoder;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.lang.reflect.Type;

@Slf4j
@Configuration
public class FeignConfiguration {


    @Autowired
    private ObjectFactory<HttpMessageConverters> messageConverters;

    /**
     * 自定义编码器 - 确保请求体正确序列化
     */
    @Bean
    public Encoder feignEncoder() {
        return new SpringEncoder(this.messageConverters);
    }

    /**
     * 自定义解码器 - 确保响应体正确反序列化
     */
    @Bean
    public Decoder feignDecoder() {
        return new OptionalDecoder(new SpringDecoder(this.messageConverters));
    }

    /**
     * 自定义错误解码器
     * org.springframework.cloud.openfeign.FeignClientsConfiguration
     */
    @Bean
    public Decoder errorDecoder() {
        return new OptionalDecoder(
                new CustomErrorDecoder(new SpringDecoder(this.messageConverters)));
    }

    /**
     * 请求拦截器 - 添加调试日志
     */
    @Bean
    public RequestInterceptor feignRequestInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                // 添加请求日志
                if (template.body() != null) {
                    String body = new String(template.body());
                    if (body.contains("loginToAdminByUser")) {
                        log.info("Feign请求体: {}", body);
                    }
                }
            }
        };
    }

    @Slf4j
    public static class CustomErrorDecoder extends ResponseEntityDecoder implements ErrorDecoder {

        @SneakyThrows
        @Override
        public Object decode(Response response, Type type) throws FeignException {
            if (response.headers().containsKey(AuthConstants.X_CUSTOM_ERROR)) {
                Object decode = null;
                try {
                    decode = super.decode(response, DtoResult.class);
                } catch (IOException e) {
                    log.error("CustomErrorDecoder =>  子服务异常了 而且还不能 decode,{}", e.getMessage(), e);
                    throw new RuntimeException("子服务解析异常，请稍后重试！");
                }
                log.error("CustomErrorDecoder =>  子服务异常了,{}", decode);
                if (decode instanceof DtoResult || decode instanceof Result) {
                    return decode;
                } else {
                    // 有X_CUSTOM_ERROR头但解析失败时，说明子服务返回了不可读的错误信息
                    // 但由于有X_CUSTOM_ERROR头，说明是子服务主动抛出的异常，应该提示具体的错误而不是"系统升级中"
                    log.warn("子服务返回了X_CUSTOM_ERROR头，但响应体解析失败，decode对象类型: {}", decode != null ? decode.getClass().getName() : "null");
                    throw new BizRuntimeException("子服务处理异常，请稍后重试！");
                }
            }
            return super.decode(response, type);
        }

        public CustomErrorDecoder(Decoder decoder) {
            super(decoder);
        }

        @Override
        public Exception decode(String methodKey, Response response) {
            log.error("Feign 请求失败：[{}] - [{}]", methodKey, response.status());
            return new BizRuntimeException("系统升级中，请稍后");
        }
    }

}
