package com.saida.services.deviceApi.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MTU响应
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonMtuResp {
    /**
     * 网卡接口名称
     */
    @JSONField(name = "interfaceName")
    private String interfaceName;
    
    /**
     * MTU值
     */
    @JSONField(name = "mtu")
    private Integer mtu;
}