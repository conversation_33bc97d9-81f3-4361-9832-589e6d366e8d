package com.saida.services.algorithm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.algorithm.dto.AlgorithmParamField;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.tools.attr.DisplayField;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 算法管理
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-29 11:27:39
 */
@Getter
@Setter
@TableName(value = "algorithm_manage", autoResultMap = true)
public class AlgorithmManageEntity extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private String code;

    /**
     *
     */
    private String name;

    /**
     * 算法类别（固定字典： algorithm_category）
     */
    @DisplayField(field = "categoryName")
    private Long category;

    /**
     * 适用场景（固定字典： algorithm_scene）
     */
    private String scene;

    @TableField(exist = false)
    private String sceneNames;

    /**
     * 算力来源（固定字典： algorithm_source）
     */
    private String source;

    @TableField(exist = false)
    private String sourceNames;

    @TableField(exist = false)
    private String shortSourceNames;

    /**
     * logo地址
     */
    private String logo;

    /**
     * 示例图
     */
    private String sampleGraph;

    /**
     * 是否设置布防  1：是， 0：否
     */
    private Integer areaDefense;

    /**
     * 状态， 1：启用， 0：禁用
     */
    private Integer status;

    /**
     * 简介
     */
    private String brief;

    /**
     * 算法参数
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<AlgorithmParamField> param;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<AlgorithmParamField> configParam;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改人
     */
    private Long updateUser;

    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    /**
     * 算法仓是否展示 0 不展示 1 展示
     */
    private Integer isShow;

    /**
     * 算法类别
     */
    private Long algorithmCategory;

    /**
     * 模型标签
     */
    private String modelLabel;

    /**
     * 演示示例（图片）
     */
    private String demoPic;

    /**
     * 演示示例（视频）
     */
    private String demoVideo;

    /**
     * 在线体验开启 0 否 1 是
     */
    private Integer onlineStatus;

    /**
     * 演示模型
     */
    private Long onlineModelType;

    /**
     * 预置演示图片
     */
    private String onlineSetPic;

    /**
     * 应用条件
     */
    private String useConditions;

    /**
     * 应用场景
     */
    private String useScene;

    @TableField(exist = false)
    private String algorithmCategoryName;

    @TableField(exist = false)
    private String onlineModelTypeName;
}