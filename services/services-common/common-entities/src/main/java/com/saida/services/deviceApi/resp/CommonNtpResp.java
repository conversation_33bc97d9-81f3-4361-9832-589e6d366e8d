package com.saida.services.deviceApi.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * NTP地址响应
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonNtpResp {
    /**
     * NTP服务器地址
     */
    @JSONField(name = "ntpServer")
    private List<String> ntpServer;
    
    /**
     * 同步间隔（单位：分钟）
     */
    @JSONField(name = "syncInterval")
    private Integer syncInterval;
    
    /**
     * 是否启用：0-禁用，1-启用
     */
    @JSONField(name = "enabled")
    private Integer enabled;
}