package com.saida.services.algorithm.resp;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 */
@Getter
@Setter
public class VlinkerAlgorithmGetAlgBoxResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 盒子id
     */
    private Long boxId;
    /**
     * 盒子类型
     */
    private Integer boxType;
    /**
     * 盒子名称
     */
    private String boxName;
    /**
     * 盒子编码
     */
    private String boxCode;
}