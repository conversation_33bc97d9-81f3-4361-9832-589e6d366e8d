package com.saida.services.algorithm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 三方平台订阅设备
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-01-03 13:53:22
 */
@Data
@TableName("sys_third_party_device")
public class ThirdPartyDeviceEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 三方平台
     */
    private Long thirdId;

    /**
     * 设备id
     */
    private Long cameraId;

    /**
     * 算法订阅，多个算法用逗号隔开
     */
    private String subscribe;
}