package com.saida.services.deviceApi.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.saida.services.deviceApi.enums.ChannelAbilityEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 通道能力开关查询响应
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonChannelAbilityResp {
    /**
     * 通道支持的能力列表
     * 使用 ChannelAbilityEnum 中定义的枚举值
     */
    @JSONField(name = "ability")
    private List<Integer> ability;
    /**
     * 设备开启的能力列表
     * 使用 DeviceAbilityEnum 中定义的枚举值
     */
    @JSONField(name = "abilityByOpen")
    private List<Integer> abilityByOpen;
    
    /**
     * 通道ID
     */
    @JSONField(name = "channelId")
    private String channelId;
    
    /**
     * 获取通道能力枚举列表
     */
    public List<ChannelAbilityEnum> getAbilityEnums() {
        if (ability == null) {
            return new ArrayList<>();
        }
        return ability.stream()
                .map(ChannelAbilityEnum::getByValue)
                .filter(item -> item != null)
                .collect(Collectors.toList());
    }
    
    /**
     * 设置通道能力枚举列表
     */
    public void setAbilityEnums(List<ChannelAbilityEnum> abilityEnums) {
        if (abilityEnums == null) {
            this.ability = new ArrayList<>();
            return;
        }
        this.ability = abilityEnums.stream()
                .map(ChannelAbilityEnum::getValue)
                .collect(Collectors.toList());
    }
}