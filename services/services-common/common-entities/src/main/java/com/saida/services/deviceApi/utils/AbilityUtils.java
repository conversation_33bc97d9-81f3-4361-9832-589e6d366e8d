package com.saida.services.deviceApi.utils;

import com.saida.services.deviceApi.enums.ChannelAbilityEnum;
import com.saida.services.deviceApi.enums.DeviceAbilityEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 能力工具类
 * <AUTHOR>
 */
public class AbilityUtils {
    
    /**
     * 将设备能力列表转换为位掩码
     * @param abilities 设备能力列表
     * @return 位掩码
     */
    public static int deviceAbilitiesToBitMask(List<DeviceAbilityEnum> abilities) {
        if (abilities == null || abilities.isEmpty()) {
            return 0;
        }
        
        int bitMask = 0;
        for (DeviceAbilityEnum ability : abilities) {
            bitMask |= (1 << (ability.getValue() - 1));
        }
        return bitMask;
    }
    
    /**
     * 将位掩码转换为设备能力列表
     * @param bitMask 位掩码
     * @return 设备能力列表
     */
    public static List<DeviceAbilityEnum> bitMaskToDeviceAbilities(int bitMask) {
        List<DeviceAbilityEnum> abilities = new ArrayList<>();
        for (DeviceAbilityEnum ability : DeviceAbilityEnum.values()) {
            if ((bitMask & (1 << (ability.getValue() - 1))) != 0) {
                abilities.add(ability);
            }
        }
        return abilities;
    }
    
    /**
     * 将通道能力列表转换为位掩码
     * @param abilities 通道能力列表
     * @return 位掩码
     */
    public static int channelAbilitiesToBitMask(List<ChannelAbilityEnum> abilities) {
        if (abilities == null || abilities.isEmpty()) {
            return 0;
        }
        
        int bitMask = 0;
        for (ChannelAbilityEnum ability : abilities) {
            bitMask |= (1 << (ability.getValue() - 1));
        }
        return bitMask;
    }
    
    /**
     * 将位掩码转换为通道能力列表
     * @param bitMask 位掩码
     * @return 通道能力列表
     */
    public static List<ChannelAbilityEnum> bitMaskToChannelAbilities(int bitMask) {
        List<ChannelAbilityEnum> abilities = new ArrayList<>();
        for (ChannelAbilityEnum ability : ChannelAbilityEnum.values()) {
            if ((bitMask & (1 << (ability.getValue() - 1))) != 0) {
                abilities.add(ability);
            }
        }
        return abilities;
    }
    
    /**
     * 检查设备能力是否启用
     * @param bitMask 位掩码
     * @param ability 设备能力
     * @return 是否启用
     */
    public static boolean isDeviceAbilityEnabled(int bitMask, DeviceAbilityEnum ability) {
        return (bitMask & (1 << (ability.getValue() - 1))) != 0;
    }
    
    /**
     * 检查通道能力是否启用
     * @param bitMask 位掩码
     * @param ability 通道能力
     * @return 是否启用
     */
    public static boolean isChannelAbilityEnabled(int bitMask, ChannelAbilityEnum ability) {
        return (bitMask & (1 << (ability.getValue() - 1))) != 0;
    }
}