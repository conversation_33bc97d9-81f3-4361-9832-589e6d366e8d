package com.saida.services.deviceApi.resp;

import lombok.Data;

import java.io.Serializable;

/**
 */
@Data
public class CommonSetStatusLightResp implements Serializable {
    private static final long serialVersionUID = 1L;

    //0 自动模式     白天全彩，夜间黑白，夜间亮红外补光灯
    //1 全彩模式     白天全彩，夜间全彩，夜间亮白光补光灯
    //2 智能模式    白天全彩，夜间默认黑白模式红外补光，当检测到有人 转全彩模式 白光补光，当人离开转黑白模式红外补光、
    public Integer mode;
    //0:关闭, 1:白光补光, 2:红外补光
    private Integer value;
    //0:状态指示灯关闭, 1:状态指示灯开启
    private Integer statusLight;
}