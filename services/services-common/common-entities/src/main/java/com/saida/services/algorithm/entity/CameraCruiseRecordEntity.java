package com.saida.services.algorithm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TableName("camera_cruise_record")
public class CameraCruiseRecordEntity extends BaseEntity<CameraCruiseRecordEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long cruiseId;

    private Long cameraId;

    private Integer prePoint;

    private Integer cruiseTime;

    private Integer rotationTime;

    private Integer sort;
}