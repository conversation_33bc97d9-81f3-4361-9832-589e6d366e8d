package com.saida.services.deviceApi.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommonSetDualCameraLinkageReq {

    /**
     * sdk 需要一个通道编号
     */
    private Integer channelNum;
    //0-100
    private Double pointsX;
    //0-100
    private Double pointsY;
    //变倍参数
    private Integer zoom;
}
