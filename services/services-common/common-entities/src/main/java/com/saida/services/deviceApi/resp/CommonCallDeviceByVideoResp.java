package com.saida.services.deviceApi.resp;

import lombok.Data;

@Data
public class CommonCallDeviceByVideoResp {

    // 对讲数据
    private String callData;

    // 视频格式
    private Integer videoFormat;

    // 音频格式
    private Integer audioFormat;

    private Double uiFps;

    /**
     * 屏幕尺寸
     */
    private Double screenSize;

    /**
     * 屏幕分辨率宽
     */
    private Integer uiWidth;

    /**
     * 屏幕分辨率高
     */
    private Integer uiHeight;

    /**
     * 每英寸像素点数 如 100、200、240、800、1200
     */
    private Integer uiDpi;

    /**
     * 是否支持触屏 0.不支持、1.支持
     */
    private Integer uiSupportTouchFlag;

    /**
     * 亮度0-100
     */
    private Integer uiScreenBrightness;

    /**
     * 0x01.JPEG 图片编码模式, 0x02.H264 编 码 ，0x04.H265 编 码 , 若 支 持 H264 、 H265 编 码 ， 则ui_video_enc_ability=0x02|0x04
     */
    private Integer uiVideoEncAbility;

    /**
     * 最大支持的码率
     */
    private Integer uiMaxBitRate;

    /**
     * 息屏时间 30s
     */
    private Integer offScreenTime;

    /**
     * gop
     */
    private Integer uiGop;

    /**
     * 握手数据 调试
     */
    private String handshakeData;
}
