package com.saida.services.exception;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 自定义异常
 * 业务异常
 */
@Data
@NoArgsConstructor
public class BizRuntimeException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private int code = 500;
    private String message = "无法处理此请求！";
    private String error = "错误日志！";

    public BizRuntimeException(Exception exception) {
        super();
    }

    public BizRuntimeException(RuntimeException exception) {
        super();
    }

    public BizRuntimeException(String msg) {
        super(msg);
        this.message = msg;
    }

    public BizRuntimeException(String msg,String error) {
        super(msg);
        this.message = msg;
        this.error = error;
    }

    public BizRuntimeException(int code,String msg,String error) {
        super(msg);
        this.code = code;
        this.message = msg;
        this.error = error;
    }


    public BizRuntimeException(int code, String msg) {
        super(msg);
        this.code = code;
        this.message = msg;
    }
}