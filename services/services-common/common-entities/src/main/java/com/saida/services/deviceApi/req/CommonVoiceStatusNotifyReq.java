package com.saida.services.deviceApi.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 语音状态通知请求
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonVoiceStatusNotifyReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 1 APP在通话过程中 主动挂断 ，2 设备给app拨打后 app主动接听  ，3 设备给app拨打后 app主动拒接
     */
    @JSONField(name = "status")
    private Integer status;

    /**
     * 通话会话ID（预留）
     */
    @JSONField(name = "sessionId")
    private String sessionId;
}