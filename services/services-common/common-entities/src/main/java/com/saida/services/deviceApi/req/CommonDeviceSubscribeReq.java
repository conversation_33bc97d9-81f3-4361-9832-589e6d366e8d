package com.saida.services.deviceApi.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 设备订阅告警
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonDeviceSubscribeReq implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 目录订阅 1订阅 0未订阅
     */
    private Boolean catalog;

    /**
     * 告警订阅 1订阅 0未订阅
     */
    private Boolean alarm;

    /**
     * 位置订阅 1订阅 0未订阅
     */
    private Boolean position;
}