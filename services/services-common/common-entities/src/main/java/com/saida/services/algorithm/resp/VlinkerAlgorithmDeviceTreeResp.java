package com.saida.services.algorithm.resp;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 视频汇聚平台-获取设备树接口响应类
 */
@Getter
@Setter
public class VlinkerAlgorithmDeviceTreeResp implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;

    private String parentId;

    private String name;

    private List<VlinkerAlgorithmDeviceTreeResp> children;


    private Boolean hasChild;
}
