package com.saida.services.algorithm.dto;

import lombok.Data;

@Data
public class DistributeAlgorithmTasksAlgDto {
    /**
     * 1开启  2关闭
     */
    private Integer type;

    /**
     * 设备id
     */
    private String deviceCode;

    /**
     * 通道id
     */
    private String channelId;

    /**
     * 开启时长
     */
    private Integer day;

    /**
     * 开启的算法类型
     * /api/algServer/getAlgorithmManageList
     */
    private Long algId;



}
