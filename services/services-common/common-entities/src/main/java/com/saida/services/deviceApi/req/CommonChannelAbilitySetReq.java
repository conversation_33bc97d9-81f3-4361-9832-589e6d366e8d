package com.saida.services.deviceApi.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.saida.services.deviceApi.enums.ChannelAbilityEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通道能力开关设置请求
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonChannelAbilitySetReq {
    /**
     * 通道能力
     * 使用 ChannelAbilityEnum 中定义的枚举值
     */
    @JSONField(name = "ability")
    private Integer ability;
    
    /**
     * 开关状态：0-关闭，1-开启
     */
    @JSONField(name = "status")
    private Integer status;
    
    /**
     * 通道ID
     */
    @JSONField(name = "channelId")
    private String channelId;
    
    /**
     * 设置通道能力（使用枚举）
     */
    public void setAbilityEnum(ChannelAbilityEnum abilityEnum) {
        if (abilityEnum != null) {
            this.ability = abilityEnum.getValue();
        }
    }
    
    /**
     * 获取通道能力枚举
     */
    public ChannelAbilityEnum getAbilityEnum() {
        return ChannelAbilityEnum.getByValue(this.ability);
    }
}