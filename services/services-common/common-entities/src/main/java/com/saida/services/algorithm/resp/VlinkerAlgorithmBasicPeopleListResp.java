package com.saida.services.algorithm.resp;

import lombok.Data;

/**
 * 算法-基础数据-人员
 */
@Data
public class VlinkerAlgorithmBasicPeopleListResp {

    /**
     * 人员id
     */
    private Long id;

    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别，1-男，2-女
     */
    private Integer sex;

    /**
     * 人员编号
     */
    private String number;

    /**
     * 人员照片，url逗号分隔
     */
    private String photos;
}
