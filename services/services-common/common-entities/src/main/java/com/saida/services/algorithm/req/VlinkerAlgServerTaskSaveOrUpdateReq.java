package com.saida.services.algorithm.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.algorithm.resp.VlinkerAlgorithmGetAlgServerTaskListResp;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class VlinkerAlgServerTaskSaveOrUpdateReq {

    private Long id;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 设备ID
     */
    private String deviceId;

    private String channelId;

    /**
     * 云服务ID
     * 不传服务端会默认分配
     */
    private Long cloudServerId;
    /**
     * 时间计划id
     * 不传默认全部
     */
    private Long timeTemplateId;

    /**
     * 云服务名称
     */
    private String cloudServerName;

    /**
     * 时间计划名称
     */
    private String timeTemplateName;

    /**
     * 设备名称
     */
    private String deviceName;

    private String channelName;

    /**
     * 算法名称
     */
    private String algorithmName;

    /**
     * 算法ID
     */
    private Long algorithmId;

    private String openAlgorithmCode;

    /**
     * 算法参数配置
     */
    private Map<String, Object> algorithmParamConfig;

    /**
     * 自动跟踪 0：跟踪 1：不跟踪
     */
    private Integer autoTrace;

    /**
     * 预设点设置信息
     */
    private List<VlinkerAlgorithmGetAlgServerTaskListResp.PresetPointVO> presetPointVOList;

    @Getter
    @Setter
    public static class PresetPointVO implements Serializable {
        private static final long serialVersionUID = 1L;


        /**
         * 预置点位id
         */
        private String presetPointId;

        /**
         * 绘制信息
         */
        private List<VlinkerAlgorithmGetAlgServerTaskListResp.PresetPointVO.DrawAreaVO> drawAreaVOList;


        @Getter
        @Setter
        public static class DrawAreaVO implements Serializable {
            private static final long serialVersionUID = 1L;


            private Long id;

            /**
             * 名称
             */
            private String name;

            /**
             * 颜色
             */
            private String color;

            /**
             * 类型 0：检测区 1：屏蔽区
             */
            private Integer type;

            /**
             * 检测规则 0：目标框触线 1：目标中心点过线
             */
            private Integer checkRule;

            /**
             * 坐标
             */
            private String coordinate;

        }

    }


    /**
     * 运行状态 0：未开始 1：运行中
     */
    private Integer status;

    /**
     * 运行时长
     */
    private Long runTime;

    /**
     * 批次ID
     */
    private Long batchId;

    private Integer notBatch;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endTime;

    /**
     * 最后运行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date runLastTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    private String ids;

    private Long deviceTreeId;

    private Integer type;

    /*
     * SQL传参- 设备ID集合
     */
    private Set<Long> deviceIdSet;


    private Long algorithmSource;


    /**
     * 要下发的算法编码
     */
    private String algCode;
    /*
     * 账号
     */
    private String appKey;
}
