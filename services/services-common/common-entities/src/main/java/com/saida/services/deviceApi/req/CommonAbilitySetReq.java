package com.saida.services.deviceApi.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.saida.services.deviceApi.enums.DeviceAbilityEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 设备能力开关设置请求
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonAbilitySetReq {
    /**
     * 设备能力
     * 使用 DeviceAbilityEnum 中定义的枚举值
     */
    @JSONField(name = "ability")
    private Integer ability;
    
    /**
     * 开关状态：0-关闭，1-开启
     */
    @JSONField(name = "status")
    private Integer status;
    
    /**
     * 设置设备能力（使用枚举）
     */
    public void setAbilityEnum(DeviceAbilityEnum abilityEnum) {
        if (abilityEnum != null) {
            this.ability = abilityEnum.getValue();
        }
    }
    
    /**
     * 获取设备能力枚举
     */
    public DeviceAbilityEnum getAbilityEnum() {
        return DeviceAbilityEnum.getByValue(this.ability);
    }
}