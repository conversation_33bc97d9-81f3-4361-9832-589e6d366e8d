package com.saida.services.deviceApi.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommonSetFileReq {
    // 文件地址
    private String fileUrl;
    // 文件hash 验证用的
    private String fileHash;
    // 文件标识
    private String fileTag;
    // 文件大小
    private Long fileSize;

    private String fileName;

    // 4 持久化 2 临时 1 sd卡
    private byte[] storagePrefer = new byte[]{4, 2, 1};

}
