package com.saida.services.algorithm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.entities.base.BaseRequest;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("sys_third_party")
public class ThirdPartyEntity extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     *
     */
    private String name;

    /**
     * 账号
     */
    private String account;

    /**
     * 秘钥
     */
    private String secretKey;

    /**
     * AES秘钥
     */
    private String aesKey;

    /**
     * 推送地址
     */
    private String pushUrl;

    /**
     * topic
     */
    private String topic;

    /**
     * 状态 1：启用， 0：禁用
     */
    private Integer status;

    // 是否feign调用：1-是；0-否
    private Integer feignReq;

    // 是否内部：1-是；0-否
    private Integer innerSystem;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改人
     */
    private Long updateUser;

    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}