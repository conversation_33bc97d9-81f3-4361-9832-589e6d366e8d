package com.saida.services.algorithm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.entities.base.BaseRequest;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 角色-资源
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-07 09:28:38
 */
@Data
@TableName("sys_role_permission")
public class RolePermissionEntity extends BaseRequest implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 角色ID
	 */
	private Long rid;
	/**
	 * 菜单ID
	 */
	private Long pid;

	/**
	 * 创建人
	 */
	private Long createUser;
	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@TableField(exist = false)
	private String pids;

	@TableField(exist = false)
	private Long appScene;

	@TableField(exist = false)
	private Long category;
}
