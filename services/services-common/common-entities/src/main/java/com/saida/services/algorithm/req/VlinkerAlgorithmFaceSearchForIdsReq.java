package com.saida.services.algorithm.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 人脸对比 1:N  在分组中比对
 */
@Data
public class VlinkerAlgorithmFaceSearchForIdsReq {


    /**
     * 账号
     */
    private String appKey;

    /**
     * 人脸1图片base64
     */
    @NotBlank(message = "人脸图片不能为空")
    private String faceImageBase64;

    /**
     * 人员ID
     */
    private List<Long> ids;

    /**
     * 相似度0-1 默认0.6
     */
    private Double similarity = 0.6;
}
