package com.saida.services.deviceApi.req;

import lombok.Data;

@Data
public class CommonSetOsdInfoReq {

    /**
     * 水印位置，支持4个角，0关闭 1：左上，2：右上，3：左下，4：右下
     */
    private Integer position;

    /**
     * logo位置，支持4个角，0关闭 1：左上，2：右上，3：左下，4：右下
     * 两个位置不能重叠！
     */
    private Integer logPosition;
    /**
     * osd是否启用,1:启用, 0:禁用
     */
    private Integer osdEnable;
    /**
     * OSD文字，使用HTML部分格式，支持字符串换行
     */
    private String osdTxt;

    /**
     * 是否开启
     */
    private Boolean textWatermark;

    /**
     * 时间osd样式，值为
     * CommonOsdTimeEnum
     */
    private Integer timeOsdFormat;
}
