package com.saida.services.algorithm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 云服务类型
 */
@Getter
@AllArgsConstructor
public enum CloudTypeEnum {
    /**
     * 羚通算法
     */
    LNTON(1, "lnton", "羚通算法"),

    /**
     * 赛达VLinker算法
     */
    VLINKER(2, "vlinker", "赛达VLinker算法");

    private final Integer type;
    private final String code;
    private final String name;

    public static CloudTypeEnum getType(Integer type) {
        for (CloudTypeEnum t : values()) {
            if (Objects.equals(type, t.getType())) {
                return t;
            }
        }
        return null;
    }

    public static String getNameByType(Integer type) {
        for (CloudTypeEnum t : values()) {
            if (Objects.equals(type, t.getType())) {
                return t.getName();
            }
        }
        return "";
    }
}