package com.saida.services.deviceApi;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * osd 时间格式枚举
 */
@Getter
@AllArgsConstructor
public enum CommonOsdTimeEnum {
    /**
     * // YYYY-MM-DD hh:mm:ss week(默认)
     * EN_SD_OSD_TIMEFORMAT_YYYYMMDDHHMMSSW      = 0,
     * // YYYY-MM-DD week hh:mm:ss
     * EN_SD_OSD_TIMEFORMAT_YYYYMMDDWHHMMSS      = 1,
     * // week YYYY-MM-DD hh:mm:ss
     * EN_SD_OSD_TIMEFORMAT_WYYYYMMDDHHMMSS      = 2,
     * // hh:mm:ss YYYY-MM-DD week
     * EN_SD_OSD_TIMEFORMAT_HHMMSSYYYYMMDDW      = 3,
     * // hh:mm:ss week YYYY-MM-DD
     * EN_SD_OSD_TIMEFORMAT_HHMMSSWYYYYMMDD      = 4,
     * // week hh:mm:ss YYYY-MM-DD
     * EN_SD_OSD_TIMEFORMAT_WHHMMSSYYYYMMDD      = 5,
     * // YYYY-MM-DD hh:mm:ss
     * EN_SD_OSD_TIMEFORMAT_YYYYMMDDHHMMSS       = 6,
     * // hh:mm:ss YYYY-MM-DD
     * EN_SD_OSD_TIMEFORMAT_HHMMSSYYYYMMDD       = 7,
     * // YYYY-MM-DD week
     * EN_SD_OSD_TIMEFORMAT_YYYYMMDDW            = 8,
     * // week YYYY-MM-DD
     * EN_SD_OSD_TIMEFORMAT_WYYYYMMDD            = 9,
     * // YYYY-MM-DD
     * EN_SD_OSD_TIMEFORMAT_YYYYMMDD             = 10,
     */

    EN_SD_OSD_TIMEFORMAT_YYYYMMDDHHMMSSW(0, "YYYY-MM-DD hh:mm:ss week(默认)"),
    EN_SD_OSD_TIMEFORMAT_YYYYMMDDWHHMMSS(1, "YYYY-MM-DD week hh:mm:ss"),
    EN_SD_OSD_TIMEFORMAT_WYYYYMMDDHHMMSS(2, "week YYYY-MM-DD hh:mm:ss"),
    EN_SD_OSD_TIMEFORMAT_HHMMSSYYYYMMDDW(3, "hh:mm:ss YYYY-MM-DD week"),
    EN_SD_OSD_TIMEFORMAT_HHMMSSWYYYYMMDD(4, "hh:mm:ss week YYYY-MM-DD"),
    EN_SD_OSD_TIMEFORMAT_WHHMMSSYYYYMMDD(5, "week hh:mm:ss YYYY-MM-DD"),
    EN_SD_OSD_TIMEFORMAT_YYYYMMDDHHMMSS(6, "YYYY-MM-DD hh:mm:ss"),
    EN_SD_OSD_TIMEFORMAT_HHMMSSYYYYMMDD(7, "hh:mm:ss YYYY-MM-DD"),
    EN_SD_OSD_TIMEFORMAT_YYYYMMDDW(8, "YYYY-MM-DD week"),
    EN_SD_OSD_TIMEFORMAT_WYYYYMMDD(9, "week YYYY-MM-DD"),
    EN_SD_OSD_TIMEFORMAT_YYYYMMDD(10, "YYYY-MM-DD");
    private final Integer type;
    private final String des;


    public static CommonOsdTimeEnum getByType(Integer type) {
        for (CommonOsdTimeEnum value : CommonOsdTimeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
