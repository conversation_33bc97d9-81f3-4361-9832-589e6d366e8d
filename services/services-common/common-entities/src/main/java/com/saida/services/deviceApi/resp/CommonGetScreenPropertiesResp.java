package com.saida.services.deviceApi.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonGetScreenPropertiesResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * {
     * "code" : 0,    //具体错误码
     * "msg" : "……" //对应错误信息描述
     * "rsp" :
     * {
     * "dScreenSize":"8.9", //屏幕尺寸",字符串如 "8.9"、"15.6"、"43.0"、"55.0"、"65.0"
     * "uiWidth": 1920 ,//整型，屏幕分辨率宽，
     * "uiHeight":1080  ,//整型，屏幕分辨率高
     * "uiDpi":100  ,//整型，每英寸像素点数 如 100、200、240、800、1200
     * "uiSupportTouchFlag":0//是否支持触屏 0.不支持、1.支持
     * "ui_screen_brightness":10  ,//亮度0-100
     * // 0x01.JPEG 图片编码模式, 0x02.H264 编 码 ，0x04.H265 编 码 , 若 支 持 H264 、 H265 编 码 ， 则ui_video_enc_ability=0x02|0x04
     * "ui_video_enc_ability":0x02  ,
     * "ui_max_bit_rate":2048  , //整型，最大支持的码率
     * "off_screen_time":30  , //息屏时间 30s
     * }
     * }
     */

    private Double dScreenSize;

    private Double uiFps;

    private Integer uiWidth;

    private Integer uiHeight;

    private Integer uiDpi;

    private Integer uiSupportTouchFlag;

    private Integer uiVideoEncAbility;

    private Integer uiMaxBitRate;

    /**
     * gop
     */
    private Integer uiGop;


    // 只会回读下面的3个参数

    /**
     *  亮度0-100
     */
    private Integer uiScreenBrightness;
    /**
     * 屏幕背景图的tag，需要下载，然后使用到屏幕上
     */
    private Integer screenSaver;
    /**
     * 息屏时间 0：永不息屏 -1: 一直息屏，其余数字代表多久后息屏 单位秒 单位秒
     */
    private Integer offScreenTime;
}
