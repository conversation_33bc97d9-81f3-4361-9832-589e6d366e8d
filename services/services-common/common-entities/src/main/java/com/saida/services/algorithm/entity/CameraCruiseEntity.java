package com.saida.services.algorithm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TableName("camera_cruise")
public class CameraCruiseEntity extends BaseEntity<CameraCruiseEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     * 设备id
     */
    private Long cameraId;

    /*
     * 名称
     */
    private String name;

    /*
     * 0：未启用 1：启用
     */
    private Integer status;

    private String appKey;
}