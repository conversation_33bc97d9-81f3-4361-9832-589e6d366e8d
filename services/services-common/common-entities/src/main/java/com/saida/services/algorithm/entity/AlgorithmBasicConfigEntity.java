package com.saida.services.algorithm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TableName("algorithm_basic_config")
public class AlgorithmBasicConfigEntity extends BaseEntity<AlgorithmBasicConfigEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     * 默认抽帧率（帧）
     */
    private String defaultFrameRate;

    /*
     * 默认抽帧时间（秒）
     */
    private String defaultFrameExtractionTime;

    /*
     * 默认识别延迟（秒）
     */
    private String defaultRecognitionDelay;

    /*
     * 此为压力测试的开关
     */
    private Integer pressureTestSwitch;
}