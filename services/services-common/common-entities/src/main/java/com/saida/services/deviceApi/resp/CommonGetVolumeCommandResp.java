package com.saida.services.deviceApi.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取音量控制响应
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonGetVolumeCommandResp {
    /**
     * 广播音量
     */
    private Integer speakerVolume;
    
    /**
     * 录音音量
     */
    private Integer micVolume;
}