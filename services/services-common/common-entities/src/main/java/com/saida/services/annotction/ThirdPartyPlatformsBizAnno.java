package com.saida.services.annotction;

import com.saida.services.open.enums.OpenThirdPartyPlatformsTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 获取第三方业务处理类 beanName 注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ThirdPartyPlatformsBizAnno {

    /*
     * beanName，第三方业务处理类 beanName 名称，必须和 @Service 括号里的值一样
     */
    String componentValue();

    /*
     * 第三方业务处理类名称
     */
    String componentName();

    /*
     * 第三方业务处理类描述
     */
    String componentDes();

    /*
     * 第三方平台类型
     */
    OpenThirdPartyPlatformsTypeEnum[] platformType() default OpenThirdPartyPlatformsTypeEnum.VIDEO;
}
