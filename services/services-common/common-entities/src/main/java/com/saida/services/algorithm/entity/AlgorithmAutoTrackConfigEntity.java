package com.saida.services.algorithm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TableName("algorithm_auto_track_config")
public class AlgorithmAutoTrackConfigEntity extends BaseEntity<AlgorithmAutoTrackConfigEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     * 是否开启：1-开启；0-关闭
     */
    private Integer enable;

    /*
     * 目标聚焦大小
     */
    private String focus;

    /*
     * 最大跟踪时长
     */
    private String maxTrackDuration;

    /*
     * ptz调整速度
     */
    private String ptzAdjustSpeed;
}