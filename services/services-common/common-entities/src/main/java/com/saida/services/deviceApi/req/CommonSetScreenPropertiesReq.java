package com.saida.services.deviceApi.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommonSetScreenPropertiesReq {
    //亮度0-100
    private Integer screenBrightness;
    //屏幕背景图的url，需要下载，然后使用到屏幕上
    private String screenSaver;
    //息屏时间 0：永不息屏 -1: 一直息屏，其余数字代表多久后息屏 单位秒
    private Integer offScreenTime;
}
