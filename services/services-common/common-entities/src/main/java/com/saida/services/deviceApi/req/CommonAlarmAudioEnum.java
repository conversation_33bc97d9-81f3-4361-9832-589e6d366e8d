package com.saida.services.deviceApi.req;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 告警音频枚举
 */
@Getter
@AllArgsConstructor
public enum CommonAlarmAudioEnum {

    AUDIO1("1","警戒音","pcma/warn.pcma", "警报"),
    AUDIO2("2","提示音","pcma/warn2.pcma", "警告文本");
    private final String id;
    private final String name;
    private final String path;
    private final String des;


    public static CommonAlarmAudioEnum getEnum(String id) {
        for (CommonAlarmAudioEnum e : CommonAlarmAudioEnum.values()) {
            if (e.getId().equals(id)) {
                return e;
            }
        }
        return null;
    }

}
