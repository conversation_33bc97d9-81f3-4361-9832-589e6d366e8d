package com.saida.services.algorithm.req;

import lombok.Data;

import java.util.List;

@Data
public class VlinkerAlgServerCruiseSaveOrUpdateReq {

    private String deviceId;
    private String channelId;
    private String appKey;


    private Long cameraId;

    private Long cruiseId;

    private String name;

    private Integer status;

    private List<CruiseRecordDto> cruiseRecordList;

    @Data
    public static class CruiseRecordDto {

        private Integer prePoint;

        private Integer cruiseTime;

        private Integer rotationTime;

        private Integer sort;
    }
}
