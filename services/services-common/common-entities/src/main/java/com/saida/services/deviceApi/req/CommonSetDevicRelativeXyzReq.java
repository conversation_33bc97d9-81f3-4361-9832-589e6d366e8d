package com.saida.services.deviceApi.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommonSetDevicRelativeXyzReq {

    /**
     * sdk 需要一个通道编号
     */
    private Integer channelNum;
    //0~1
    private Float pointsX;
    //0~1
    private Float pointsY;
    //变倍参数 -1~1
    private Float zoom;
}
