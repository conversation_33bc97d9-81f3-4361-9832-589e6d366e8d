package com.saida.services.deviceApi.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommonSetDormancyReq {
    //是否使能，0关闭，1使能
    private Integer enable;
    /**
     * 时间段["10:00-12:00",...]
     * 没值并且开启 底层会默认00:00-23:59
     */
    private List<String> sleepTimer;
}
