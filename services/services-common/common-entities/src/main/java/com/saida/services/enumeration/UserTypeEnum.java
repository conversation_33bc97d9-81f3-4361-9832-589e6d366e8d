package com.saida.services.enumeration;

public enum UserTypeEnum {

    // 管理用户
    GUAN_LI("user_type", 1L),

    // 客户主账户
    ZHU_ZHANG_HU("user_type", 2L),

    // 客户子账户
    ZI_ZHANG_HU("user_type", 3L),

    // ToC
    TO_C("user_type", 4L);

    public final String type;
    public final Long tag;

    UserTypeEnum(String type, Long tag) {
        this.type = type;
        this.tag = tag;
    }
}