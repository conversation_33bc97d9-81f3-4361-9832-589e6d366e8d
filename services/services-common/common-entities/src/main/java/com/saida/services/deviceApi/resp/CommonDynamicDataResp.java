package com.saida.services.deviceApi.resp;

import lombok.Data;

@Data
public class CommonDynamicDataResp {


    /**
     * @note 读取动态的数据
     * @note 请求JSON:
     * @note {
     * @note }
     * @note 响应JSON:
     * @note {
     * @note			"battery": {
     * @note				"remainingPower": 30.1,        // 电池剩余电量，单位百分比（%）
     * @note				"charging": true,              // 是否正在充电
     * @note				"cycleCount": 120              // 电池循环次数（充放电完整周期计数）
     * @note			},
     * @note			"wifi": {
     * @note				"ssid": "MyWiFi-5G",           // 当前连接的 WiFi 名称（SSID）
     * @note				"rssi": -48                    // 信号强度（RSSI，单位 dBm，通常范围为 -30 到 -90）
     * @note			},
     * @note			"lte": {
     * @note				"rsrp": -97                    // 4G 信号强度（RSRP，单位 dBm，通常范围为 -44 到 -140）
     * @note			},
     * @note			"sdCard": {
     * @note				"mounted": true,              // SD 卡是否已挂载
     * @note				"totalSize": 64000,           // SD 卡总容量（单位 MB）
     * @note				"freeSize": 12345             // SD 卡剩余容量（单位 MB）
     * @note			}
     * @note	}
     */


    private Battery battery;
    private Wifi wifi;
    private Lte lte;
    private SdCard sdCard;

    @Data
    public static class Battery {
        private Double remainingPower;
        private Boolean charging;
        private Integer cycleCount;
    }

    @Data
    public static class Wifi {
        private String ssid;
        private Integer rssi;
    }

    @Data
    public static class Lte {
        private Integer rsrp;
    }

    @Data
    public static class SdCard {
        private Boolean mounted;
        private Integer totalSize;
        private Integer freeSize;
    }

}
