package com.saida.services.algorithm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.tools.attr.DisplayField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 菜单
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-09-25 17:36:34
 */
@Data
@TableName("sys_permission")
public class PermissionEntity extends BaseRequest implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 父ID
	 */
	private Long parentId;
	/**
	 * ID链
	 */
	private String idChain;
	/**
	 * 名称
	 */
	private String name;
	/**
	 * 权限类型
	 */
	@DisplayField(field = "typeName")
	private Long type;
	/**
	 * 路由地址
	 */
	private String url;
	/**
	 * 打开方式
	 */
	private Long openWay;
	/**
	 * 图标
	 */
	private String icon;
	/**
	 * 排序
	 */
	private Integer sort;
	/**
	 * 状态，1：启用，0：禁用
	 */
	private Integer status;
	/**
	 * 创建人
	 */
	private Long createUser;
	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;
	/**
	 * 修改人
	 */
	private Long updateUser;
	/**
	 * 修改时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;

	/**
	 * 是否隐藏，0：否，1：是
	 */
	private Integer hidden;
}
