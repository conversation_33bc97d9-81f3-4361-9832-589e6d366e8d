package com.saida.services.deviceApi.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonGetDeviceInfoResp implements Serializable {
    private static final long serialVersionUID = 1L;

    // 设备 编号
    private String deviceCode;
    // 设备 sn
    private String deviceSn;
    private String channelTotal;
    // 设备软件版本号
    private String version;
    // 设备型号
    private String model;
    // 内网ip
    private String eip;
    // 外网ip
    private String iip;
    // 设备mac
    private String mac;
    // 移动通道
    private List<Integer> moveCameras;
    // 固定通道
    private List<Integer> fixedCameras;
    // wifi
    private String wifiName;
    private String wifiSsid;
    // ntp地址
    private String ntpAddr;
    // dns
    private String dns;
}
