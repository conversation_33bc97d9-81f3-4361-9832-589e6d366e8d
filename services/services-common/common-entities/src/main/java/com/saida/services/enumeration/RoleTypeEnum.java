package com.saida.services.enumeration;

public enum RoleTypeEnum {


    /**
     * 运营角色
     */
    YUN_YING("role_type", 1L),
    /**
     * 企业角色
     */
    QI_YE("role_type", 2L),
    /**
     * 用户角色
     */
    YONG_HU("role_type", 3L);

    public final String type;
    public final Long tag;

    RoleTypeEnum(String type, Long tag) {
        this.type = type;
        this.tag = tag;
    }


}
