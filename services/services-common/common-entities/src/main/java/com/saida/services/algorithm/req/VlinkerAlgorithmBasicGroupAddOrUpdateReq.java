package com.saida.services.algorithm.req;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 算法-基础人员分组编辑
 */
@Data
public class VlinkerAlgorithmBasicGroupAddOrUpdateReq {

    /**
     * 账号
     */
    private String appKey;

    /**
     * 人员库分组id
     */
    private Long id;

    /**
     * 基础人员分组名称
     */
    @NotBlank(message = "分组名称不能为空")
    @Length(max = 255, message = "分组名称长度超过限制")
    private String name;
}
