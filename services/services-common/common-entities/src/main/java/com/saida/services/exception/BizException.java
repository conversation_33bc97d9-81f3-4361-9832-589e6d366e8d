package com.saida.services.exception;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 自定义异常
 * 业务异常
 *
 */
@Data
@NoArgsConstructor
public class BizException extends Exception {
	private static final long serialVersionUID = 1L;

	private int code = 500;
    private String message;
	private Object data;

	public BizException(int code, String msg, Object data) {
		super(msg);
		this.code = code;
		this.message = msg;
		this.data = data;
	}

	public BizException(String msg) {
		super(msg);
		this.message = msg;
	}

}
