package com.saida.services.enumeration;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 第三方平台类型枚举类
 */
@Getter
@AllArgsConstructor
public enum SrvUserTypeV2Enum {

    GUAN_LI("管理运营账户", 1L),
    ZHU_ZHANG_HU("企业主账户", 2L),
    ZI_ZHANG_HU("企业子账户", 3L),
    TO_C("TOC账户", 4L);

    private final Long type = 100007L;
    private final String name;
    private final Long tag;

    public Long getDicId() {
        return Long.parseLong(String.format("%s%s", this.type, String.format("%05d", this.tag)));
    }
}