package com.saida.services.deviceApi.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 音量控制请求
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonSetVolumeCommandReq {
    /**
     * 广播音量
     */
    private Integer speakerVolume;
    
    /**
     * 录音音量
     */
    private Integer micVolume;
}