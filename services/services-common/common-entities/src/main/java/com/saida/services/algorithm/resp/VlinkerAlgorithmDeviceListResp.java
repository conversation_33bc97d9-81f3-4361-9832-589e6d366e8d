package com.saida.services.algorithm.resp;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 视频汇聚平台-获取设备列表接口请求类
 */
@Getter
@Setter
public class VlinkerAlgorithmDeviceListResp implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<DeviceInfo> records;

    private String total;

    private String size;

    private String current;

    private String pages;

    @Getter
    @Setter
    public static class DeviceInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long id;
        private Long regionId;
        private String thirdCode;
        private String name;
        private Long manufacturer;
        private String manufacturerName;
        private String ip;
        private Integer port;
        private String username;
        private String password;
        private String mainStream;
        private String subStream;
        private Long box;
        private String boxName;

        /*
         * 是否支持云台控制
         */
        private Boolean ptz = false;

        /*
         * 是否支持语音对讲
         */
        private Boolean talkback = false;
    }
}