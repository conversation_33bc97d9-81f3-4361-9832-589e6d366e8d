package com.saida.services.deviceApi.req;

import lombok.Data;

@Data
public class CommonSetOsdInfoResp {

    /**
     * 水印位置，支持4个角，1：左上，2：右上，3：左下，4：右下
     */
    private Integer position;
    /**
     * osd是否启用,1:启用, 0:禁用
     */
    private Integer osdEnable;
    /**
     * OSD文字，使用HTML部分格式，支持字符串换行
     */
    private String osdTxt;

    /**
     * 时间osd样式，值为
     * CommonOsdTimeEnum
     */
    private Integer timeOsdFormat;
}
