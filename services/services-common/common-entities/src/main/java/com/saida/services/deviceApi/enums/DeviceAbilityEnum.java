package com.saida.services.deviceApi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备能力枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DeviceAbilityEnum {
    
    /**
     * 1400协议
     */
    PROTOCOL_1400(1, "1400协议"),
    
    /**
     * Telnet
     */
    TELNET(2, "Telnet"),
    
    /**
     * 后台网页
     */
    BACKGROUND_WEB(3, "后台网页"),
    
    /**
     * 声音震慑
     */
    SOUND_SHOCK(4, "声音震慑"),
    
    /**
     * 闪光灯震慑
     */
    LIGHT_SHOCK(5, "闪光灯震慑");
    
    /**
     * 能力值
     */
    private final Integer value;
    
    /**
     * 能力描述
     */
    private final String desc;
    
    /**
     * 根据能力值获取枚举
     */
    public static DeviceAbilityEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (DeviceAbilityEnum ability : values()) {
            if (ability.getValue().equals(value)) {
                return ability;
            }
        }
        return null;
    }
    
    /**
     * 获取所有设备能力
     */
    public static List<DeviceAbilityEnum> getAllAbilities() {
        return Arrays.asList(values());
    }
    
    /**
     * 获取所有设备能力值
     */
    public static List<Integer> getAllAbilityValues() {
        return Arrays.stream(values())
                .map(DeviceAbilityEnum::getValue)
                .collect(Collectors.toList());
    }
}