package com.saida.services.deviceApi.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonGetDeviceFileResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     */
    // 还能写入多少字节的文件
    private Long writableSpaceRemaining;

    private List<FileListDto> fileList;

    @Data
    public static class FileListDto {
        private String fileName;
        private String fileHash;
        private Long fileSize;
    }

}
