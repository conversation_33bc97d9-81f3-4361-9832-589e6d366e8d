package com.saida.services.deviceApi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 通道能力枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChannelAbilityEnum {

    // // 1、云台  2、语音对讲  3、视频通话  4、人形标记
    /**
     * 云台
     */

    CLOUD_TOWER(1, "云台"),

    /**
     * 语音对讲
     */
    VOICE_INTERCOM(2, "语音对讲"),

    /**
     * 视频通话
     */
    VIDEO_CALL(3, "视频通话"),

    /**
     * 人形标记
     */
    PERSON_MARK(4, "人形标记"),

    ;
    
    /**
     * 能力值
     */
    private final Integer value;
    
    /**
     * 能力描述
     */
    private final String desc;
    
    /**
     * 根据能力值获取枚举
     */
    public static ChannelAbilityEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (ChannelAbilityEnum ability : values()) {
            if (ability.getValue().equals(value)) {
                return ability;
            }
        }
        return null;
    }
    
    /**
     * 获取所有通道能力
     */
    public static List<ChannelAbilityEnum> getAllAbilities() {
        return Arrays.asList(values());
    }
    
    /**
     * 获取所有通道能力值
     */
    public static List<Integer> getAllAbilityValues() {
        return Arrays.stream(values())
                .map(ChannelAbilityEnum::getValue)
                .collect(Collectors.toList());
    }
}