package com.saida.services.algorithm.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.saida.services.entities.base.BaseRequest;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@JsonInclude(JsonInclude.Include.ALWAYS)
@Getter
@Setter
@TableName("image_recognition")
public class ImageRecognitionEntity extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /*
     * 编号
     */
    private String number;

    /*
     * 状态， 1：完成， 0：失败
     */
    private Integer status;

    /*
     * 原始图片url
     */
    private String baseImgUrl;

    /*
     * 是否删除 1 是 0 否
     */
    private Integer isDelete;

    /*
     * 分析过后url
     */
    private String analysisImgUrl;

    /*
     * 识别算法名称
     */
    private String recoAlgorithm;

    /**
     * 分析结果json
     */
    private String analysisResult;


    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改人
     */
    private Long updateUser;

    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    @JsonIgnore
    @TableField(exist = false)
    private String begTime;
    @JsonIgnore
    @TableField(exist = false)
    private String endTime;

    @TableField(exist = false)
    private String recoAlgorithmName;


}
