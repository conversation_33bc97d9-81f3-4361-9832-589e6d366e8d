package com.saida.services.algorithm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 算法关联表
 */
@Getter
@Setter
@TableName("algorithm_mapping")
public class AlgorithmMappingEntity extends BaseEntity<AlgorithmMappingEntity> {
    private static final long serialVersionUID = 1L;

    /*
     * 算法ID
     */
    private Long algorithmId;

    /*
     * 来源ID
     */
    private String sourceId;

    /*
     * 算法名称
     */
    private String name;

    /*
     * 算法编码
     */
    private String code;

    /*
     * 算法小编码
     */
    private String minCode;
}