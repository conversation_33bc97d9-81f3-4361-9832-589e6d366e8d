package com.saida.services.algorithm.resp;

import com.saida.services.enums.ResultEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 视频汇聚平台-基础响应类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VlinkerAlgorithmBaseResp<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private String code;

    private String message;

    private T data;

    public boolean ok() {
        return String.valueOf(ResultEnum.SUCCESS.getCode()).equals(this.getCode());
    }
}