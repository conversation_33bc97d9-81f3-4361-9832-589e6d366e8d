package com.saida.services.algorithm.resp;

import lombok.Data;

import java.util.List;

@Data
public class VlinkerAlgServerCruiseListResp {

    public Long id;
    /*
     * 名称
     */
    private String name;

    /*
     * 0：未启用 1：启用
     */
    private Integer status;

    private List<CruiseRecordDto> cruiseRecordList;

    @Data
    public static class CruiseRecordDto {

        private Integer prePoint;

        private Integer cruiseTime;

        private Integer rotationTime;

        private Integer sort;
    }
}
