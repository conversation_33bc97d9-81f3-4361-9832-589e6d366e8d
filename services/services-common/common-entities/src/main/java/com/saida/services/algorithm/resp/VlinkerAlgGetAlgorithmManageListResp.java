package com.saida.services.algorithm.resp;

import com.saida.services.tools.attr.DisplayField;
import lombok.Data;

@Data
public class VlinkerAlgGetAlgorithmManageListResp {

    private Long id;

    private String code;

    /**
     *
     */
    private String name;

    /**
     * 算法类别（固定字典： algorithm_category）
     */
    @DisplayField(field = "categoryName")
    private Long category;

    /**
     * 适用场景（固定字典： algorithm_scene）
     */
    private String scene;

    /**
     * 算力来源（固定字典： algorithm_source）
     */
    private String source;

    /**
     * logo地址
     */
    private String logo;

    /**
     * 示例图
     */
    private String sampleGraph;

    /**
     * 是否设置布防  1：是， 0：否
     */
    private Integer areaDefense;

    /**
     * 状态， 1：启用， 0：禁用
     */
    private Integer status;

    /**
     * 简介
     */
    private String brief;

    /**
     * 算法仓是否展示 0 不展示 1 展示
     */
    private Integer isShow;

    /**
     * 算法类别
     */
    private String algorithmCategory;

    /**
     * 模型标签
     */
    private String modelLabel;

    /**
     * 演示示例（图片）
     */
    private String demoPic;

    /**
     * 演示示例（视频）
     */
    private String demoVideo;

    /**
     * 在线体验开启 0 否 1 是
     */
    private Integer onlineStatus;

    /**
     * 演示模型
     */
    private Long onlineModelType;

    /**
     * 预置演示图片
     */
    private String onlineSetPic;

    /**
     * 应用条件
     */
    private String useConditions;

    /**
     * 应用场景
     */
    private String useScene;

}
