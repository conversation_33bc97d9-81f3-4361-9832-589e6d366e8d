package com.saida.services.algorithm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.entities.base.BaseRequest;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * app版本表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-26 14:52:59
 */
@Data
@TableName("sys_app_version")
public class AppVersionEntity extends BaseRequest implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 更新内容
	 */
	private String content;
	/**
	 * 版本号
	 */
	private String version;
	/**
	 * 文件大小
	 */
	private Integer fileLength;
	/**
	 * 文件url
	 */
	private String fileUrl;
	/**
	 * 创建人
	 */
	private Long createUser;
	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;
	/**
	 * 修改人
	 */
	private Long updateUser;
	/**
	 * 修改时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;
	/**
	 * 更新方式，0：软更新，1：硬更新
	 */
	private Integer isForced;

	/**
	 * 状态，0：禁用，1：启用，默认启用
	 */
	private Integer status;
}
