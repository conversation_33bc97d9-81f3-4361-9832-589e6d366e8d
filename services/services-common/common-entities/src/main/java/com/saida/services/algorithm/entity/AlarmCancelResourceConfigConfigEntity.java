package com.saida.services.algorithm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TableName("alarm_cancel_resource_config")
public class AlarmCancelResourceConfigConfigEntity extends BaseEntity<AlarmCancelResourceConfigConfigEntity> implements Serializable {
    private static final long serialVersionUID = 1L;


    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /*
     * 是否开启：1-开启；0-关闭
     */
    private Integer enable;

    /*
     * 作废原因
     */
    private String resource;

    /*
     * 标签
     */
    private Long label;

    /*
     * 排序
     */
    private Integer sort;

    @TableField(exist = false)
    private String labelName;
}