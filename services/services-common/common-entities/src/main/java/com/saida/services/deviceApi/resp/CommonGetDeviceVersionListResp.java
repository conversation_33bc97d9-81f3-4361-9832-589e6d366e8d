package com.saida.services.deviceApi.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.common.entity.BasePageInfoEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class CommonGetDeviceVersionListResp {

    private BasePageInfoEntity<DeviceVersionListResp> pageInfo;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DeviceVersionListResp {
        private String version;

        private String remark;

        private Integer type;

        /**
         * 修改时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date updateTime;


        public String getTypeName() {
            if (type == 1) {
                return "手动更新";
            } else if (type == 2) {
                return "自动更新";
            } else {
                return "未知";
            }
        }
    }
}
