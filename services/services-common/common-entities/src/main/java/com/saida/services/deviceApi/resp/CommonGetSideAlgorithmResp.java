package com.saida.services.deviceApi.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class CommonGetSideAlgorithmResp {

    private List<AlarmParameterVo> alarmParameterVos;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlarmParameterVo {
        //报警区域编号
        private Integer detectId;
        //报警区域是否启用,1: 启用, 0: 禁用
        private Integer detectEnable;

        //抓图时间段  ["09:00-10:00"]
        private List<String> snapTime;
        //告警时间段 ["09:00-10:00"]
        private List<String> alarmTime;
        //0x1 声音报警，0x2 灯光报警   ，0x1 | 0x2 同时报警
        private Integer alarmType;
        //报警间隔： 单位秒
        private Integer alarmInterval;
        //0：跟踪， 1不跟踪
        private Integer alarmTrace;
        //灯光显示时间 秒
        private Integer lightingTime;

        // 声光报警时间
        private List<String> acoustoOpticTime;

        // 声光报警文件
        private String acoustoFileUrl;
        /**
         * 赛达:
         * 1,人型侦测, 2,移动侦测 3,视频遮挡 4,区域入侵
         * 区域入侵可以多个点
         * 人形检测和移动检测支持4个点 而且得是个方形
         */
        private Integer detectType;
        //报警区域侦测灵敏度,范围1-100
        private Integer detectSens;
        //报警区域坐标数组，最多10
        private List<PointVo> detectPoints;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PointVo {
        // 0 - 100的百分比
        private Double x;
        // 0 - 100的百分比
        private Double y;
    }


    public Boolean getOpenHumanTypeDetection() {
        if (alarmParameterVos == null) {
            return false;
        }
        return alarmParameterVos.stream().anyMatch(alarmParameterVo -> alarmParameterVo.getDetectType() == 1);
    }

    public Boolean getOpenMotionDetection() {
        if (alarmParameterVos == null) {
            return false;
        }
        return alarmParameterVos.stream().anyMatch(alarmParameterVo -> alarmParameterVo.getDetectType() == 2);
    }

    public Boolean getOpenVideoOcclusion() {
        if (alarmParameterVos == null) {
            return false;
        }
        return alarmParameterVos.stream().anyMatch(alarmParameterVo -> alarmParameterVo.getDetectType() == 3);
    }

    public Boolean getOpenRegionalInvasion() {
        if (alarmParameterVos == null) {
            return false;
        }
        return alarmParameterVos.stream().anyMatch(alarmParameterVo -> alarmParameterVo.getDetectType() == 4);
    }


}
