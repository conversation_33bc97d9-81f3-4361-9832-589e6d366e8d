package com.saida.services.algorithm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

@Getter
@Setter
@TableName(value = "algorithm_review_config", autoResultMap = true)
public class AlgAlgorithmReviewConfigEntity extends BaseEntity<AlgAlgorithmReviewConfigEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer enable;

    private Integer allAlgorithm;

    private Integer allDevice;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<String> algorithmList;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<String> deviceList;
}