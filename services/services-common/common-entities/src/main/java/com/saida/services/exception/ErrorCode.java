package com.saida.services.exception;

import lombok.Data;

/**
 * <AUTHOR>
 * 各个模块错误码错误信息对象
 * <p>System模块错误码1001001---1001999</p>
 * @version ErrorCode v1.0.0
 * @since 2023/08/16 09:39:00
 */
@Data
public class ErrorCode {
    /**
     * 错误码
     */
    private final Integer code;
    /**
     * 错误提示
     */
    private final String msg;

    public ErrorCode(Integer code, String message) {
        this.code = code;
        this.msg = message;
    }
}
