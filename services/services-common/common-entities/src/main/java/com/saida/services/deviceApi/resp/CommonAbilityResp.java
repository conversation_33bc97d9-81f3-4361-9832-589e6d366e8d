package com.saida.services.deviceApi.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.saida.services.deviceApi.enums.DeviceAbilityEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备能力开关查询响应
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonAbilityResp {
    /**
     * 设备支持的能力列表
     * 使用 DeviceAbilityEnum 中定义的枚举值
     */
    @JSONField(name = "ability")
    private List<Integer> ability;
    /**
     * 设备开启的能力列表
     * 使用 DeviceAbilityEnum 中定义的枚举值
     */
    @JSONField(name = "abilityByOpen")
    private List<Integer> abilityByOpen;
    
    /**
     * 获取设备能力枚举列表
     */
    public List<DeviceAbilityEnum> getAbilityEnums() {
        if (ability == null) {
            return new ArrayList<>();
        }
        return ability.stream()
                .map(DeviceAbilityEnum::getByValue)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    /**
     * 设置设备能力枚举列表
     */
    public void setAbilityEnums(List<DeviceAbilityEnum> abilityEnums) {
        if (abilityEnums == null) {
            this.ability = new ArrayList<>();
            return;
        }
        this.ability = abilityEnums.stream()
                .map(DeviceAbilityEnum::getValue)
                .collect(Collectors.toList());
    }
}