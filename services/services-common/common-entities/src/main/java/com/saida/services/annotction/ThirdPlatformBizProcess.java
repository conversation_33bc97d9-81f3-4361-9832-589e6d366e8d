package com.saida.services.annotction;


import com.saida.services.srv.enums.SrvCameraPlatformTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 获取第三方业务处理类 beanName 注解
 *
 * @author: yj
 * @date: 2023/10/27 09:19
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ThirdPlatformBizProcess {

    /*
     * beanName，第三方业务处理类 beanName 名称，必须和 @Service 括号里的值一样
     */
    String componentName();

    /*
     * 第三方业务处理类描述
     */
    String componentDes();

    /*
     * 第三方平台类型
     */
    SrvCameraPlatformTypeEnum platformType() default SrvCameraPlatformTypeEnum.TYPE_1;

}
