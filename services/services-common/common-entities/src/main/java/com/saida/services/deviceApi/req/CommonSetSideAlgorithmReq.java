package com.saida.services.deviceApi.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class CommonSetSideAlgorithmReq {

    //报警区域编号 赛达sdk新版本弃用了
    private Integer detectId;
    //抓图时间段
    private List<String> snapTime;
    //告警时间段
    private List<String> alarmTime;

    // 声光报警时间
    private List<String> acoustoOpticTime;

    // 声光报警文件
    private String acoustoFileUrl;
    //0关闭 0x1 声音报警，0x2 灯光报警   ，0x1 | 0x2 同时报警
    private Integer alarmType;
    // 灯光闪烁还是长亮 1 闪烁 2 长亮
    private Integer lightingType;
    //音频播放时间
    private Integer audioTime;
    //报警间隔： 单位秒
    private Integer alarmInterval;
    //0：跟踪， 1不跟踪
    private Integer alarmTrace;
    //灯光显示时间 秒
    private Integer lightingTime;

    //报警区域是否启用,1: 启用, 0: 禁用
    private Integer detectEnable;

    /**
     * 1 赛达  人形检测
     * 2 赛达  移动检测
     * 3 赛达  视频遮挡
     * 4 赛达  区域入侵
     */
    private Integer detectType;
    //报警区域侦测灵敏度,范围1-100
    private Integer detectSens;
    //报警区域坐标数组，最多10
    private List<PointVo> detectPoints;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PointVo {
        //0 - 100的百分比
        private Double x;
        //0 - 100的百分比
        private Double y;
    }
}
