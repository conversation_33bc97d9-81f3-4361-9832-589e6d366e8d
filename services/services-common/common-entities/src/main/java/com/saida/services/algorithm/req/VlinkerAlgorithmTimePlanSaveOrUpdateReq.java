package com.saida.services.algorithm.req;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class VlinkerAlgorithmTimePlanSaveOrUpdateReq implements Serializable {
    private static final long serialVersionUID = 1L;
    /*
     * 账号
     */
    private String appKey;

    /*
     * 模版名称
     */
    private Long id;
    /*
     * 模版名称
     */
    private String name;

    private List<TimeVO> timeList;


    @Data
    public static class TimeVO implements Serializable {
        private static final long serialVersionUID = 1L;

        /*
         * 0：周日 1：周一 2：周二 3：周三 4：周四 5：周五 6：周六
         */
        private Integer weekIndex;

        private List<TimeIntervalVO> timeList;

    }

    @Data
    public static class TimeIntervalVO implements Serializable {
        private static final long serialVersionUID = 1L;

        /*
         * 开始时间
         */
        private String startTime;

        /**
         * 结束时间
         */
        private String endTime;

    }

}