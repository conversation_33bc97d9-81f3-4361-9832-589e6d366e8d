package com.saida.services.deviceApi.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 网卡信息响应
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonNetworkInterfaceResp {
    /**
     * 网卡接口列表
     */
    @JSONField(name = "interfaces")
    private List<NetworkInterface> interfaces;
    
    /**
     * 网卡接口信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NetworkInterface {
        /**
         * 网卡名称
         */
        @JSONField(name = "name")
        private String name;
        
        /**
         * 网卡类型
         */
        @JSONField(name = "type")
        private String type;
        
        /**
         * MAC地址
         */
        @JSONField(name = "mac")
        private String mac;
        
        /**
         * IP地址
         */
        @JSONField(name = "ip")
        private String ip;
        
        /**
         * MTU值
         */
        @JSONField(name = "mtu")
        private Integer mtu;
        
        /**
         * 是否启用
         */
        @JSONField(name = "up")
        private Boolean up;
        
        /**
         * 是否为默认网卡
         */
        @JSONField(name = "default")
        private Boolean defaultInterface;
    }
}