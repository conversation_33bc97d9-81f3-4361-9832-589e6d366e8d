package com.saida.services.enumeration;

/**
 * 消息通知类型
 */
public enum UserMsgTypeNoticeEnum {
    /**
     * 设备上线
     */
    device_online("user_msg_type_notice", 1L),
    /**
     * 设备下线
     */
    device_offline("user_msg_type_notice", 2L),
    /**
     * 账号到期
     */
    account_expiration("user_msg_type_notice", 3L);

    public final String type;
    public final Long tag;

    UserMsgTypeNoticeEnum(String type, Long tag) {
        this.type = type;
        this.tag = tag;
    }
}
