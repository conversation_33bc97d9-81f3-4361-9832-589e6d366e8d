//package com.saida.services.algorithm.entity;
//
//import com.baomidou.mybatisplus.annotation.IdType;
//import com.baomidou.mybatisplus.annotation.TableField;
//import com.baomidou.mybatisplus.annotation.TableId;
//import com.baomidou.mybatisplus.annotation.TableName;
//import com.fasterxml.jackson.annotation.JsonFormat;
//import com.saida.services.entities.base.BaseRequest;
//import lombok.Data;
//import org.springframework.format.annotation.DateTimeFormat;
//
//import java.io.Serializable;
//import java.util.Date;
//
///**
// * 行政地区
// *
// * <AUTHOR>
// * @email ${email}
// * @date 2023-11-17 15:17:03
// */
//@Data
//@TableName("sys_region")
//public class RegionEntity extends BaseRequest implements Serializable {
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * ID
//     */
//    @TableId(value = "id", type = IdType.ASSIGN_ID)
//    private Long id;
//
//    /**
//     * 父级ID
//     */
//    private Long parentId;
//
//    /**
//     * 名称
//     */
//    private String name;
//
//    /**
//     * 编码
//     */
//    private String code;
//
//    /**
//     * ID链
//     */
//    private String idChain;
//
//    /**
//     * 排序
//     */
//    private Integer sort;
//
//    /**
//     * 创建人
//     */
//    private Long createUser;
//    /**
//     * 创建时间
//     */
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date createTime;
//    /**
//     * 修改人
//     */
//    private Long updateUser;
//    /**
//     * 修改时间
//     */
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date updateTime;
//
//    /**
//     * 是否还有下级 true：是， false：否
//     */
//    @TableField(exist = false)
//    private Boolean hasChild = false;
//
//    /**
//     * 是否有设备
//     */
//    @TableField(exist = false)
//    private Boolean hasDevice = false;
//
//    /**
//     * 父节点名称
//     */
//    @TableField(exist = false)
//    private String parentName;
//}
