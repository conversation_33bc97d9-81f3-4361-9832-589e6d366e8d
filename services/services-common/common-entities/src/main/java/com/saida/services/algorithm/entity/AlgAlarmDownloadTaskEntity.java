package com.saida.services.algorithm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TableName("alarm_download_task")
public class AlgAlarmDownloadTaskEntity extends BaseEntity<AlgAlarmDownloadTaskEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    private String name;

    private Integer dataQuantity;

    private String timeConsuming;

    private String fileSize;

    private Integer status;

    private String fileUrl;
}