package com.saida.services.algorithm.req;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 算法-基础数据-人员
 */
@Data
public class VlinkerAlgorithmBasicPeopleAddOrUpdateReq {

    /**
     * 账号
     */
    private String appKey;

    @NotNull(message = "人员可分组不能为空")
    private Long groupId;

    /**
     * 人员id 更新时需要
     */
    private Long id;

    /**
     * 基础人员分组名称
     */
    @NotBlank(message = "姓名不能为空")
    @Length(max = 32, message = "姓名长度超过限制")
    private String name;

    /**
     * 性别，1-男，2-女
     */
    private Integer sex;

    /**
     * 人员编号
     */
    @Length(max = 255, message = "人员编号长度超过限制")
    @NotBlank(message = "人员编号不能为空")
    private String number;

    /**
     * 照片列表，url逗号分隔
     * 1.照片须为半身照或大头照
     * 2.面部区域像素不低于128 X 128
     * 3.人脸大小占整张照片1/3以上
     * 4.图片格式支持JPG、JPEG、PNG 或 BMP
     * 5.每张图片大小须小于4M，大于2K
     * 6.图片最大尺寸 ≤ 4096*4096
     * 7.图片最小尺寸 ≥ 100*100
     */
    @NotBlank(message = "人员照片不能为空")
    private String photos;


}
