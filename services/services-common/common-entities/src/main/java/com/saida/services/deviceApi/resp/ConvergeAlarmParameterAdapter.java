package com.saida.services.deviceApi.resp;

import com.alibaba.fastjson.JSON;
import com.saida.services.converge.qxNode.resp.sd.ConvergeAlarmParameterRespV2;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ConvergeAlarmParameterAdapter {

    public static CommonGetSideAlgorithmResp adapt(String jsonRsp, Integer ver, Integer filterType) {
        CommonGetSideAlgorithmResp sideAlgorithmResp = new CommonGetSideAlgorithmResp();

        ConvergeAlarmParameterRespV2 resp = JSON.parseObject(jsonRsp, ConvergeAlarmParameterRespV2.class);
        if (resp == null || CollectionUtils.isEmpty(resp.getDetect())) return sideAlgorithmResp;

        List<CommonGetSideAlgorithmResp.AlarmParameterVo> collect = resp.getDetect().stream()
                .filter(e -> filterType == null || filterType.equals(e.getDetectType()))
                .map(e -> CommonGetSideAlgorithmResp.AlarmParameterVo.builder()
                        .detectId(e.getChannelId())
                        .detectEnable(e.getDetectEnable())
                        .detectType(e.getDetectType())
                        .alarmTrace(e.getAlarmTrace())
                        .alarmInterval(e.getAlarmInterval())
                        .alarmType(e.getAlarmType())
                        .snapTime(e.getSnapTime())
                        .alarmTime(e.getAlarmTime())
                        .acoustoFileUrl(e.getAcoustoFileUrl())
                        .acoustoOpticTime(e.getAcoustoOpticTime())
                        .detectSens(e.getDetectSens())
                        .detectPoints(CollectionUtils.isEmpty(e.getDetectPoints()) ? new ArrayList<>() :
                                e.getDetectPoints().stream().map(p ->
                                        CommonGetSideAlgorithmResp.PointVo.builder()
                                                .x(p.getX())
                                                .y(p.getY())
                                                .build()).collect(Collectors.toList()))
                        .build())
                .collect(Collectors.toList());

        sideAlgorithmResp.setAlarmParameterVos(collect);

        return sideAlgorithmResp;
    }
}
