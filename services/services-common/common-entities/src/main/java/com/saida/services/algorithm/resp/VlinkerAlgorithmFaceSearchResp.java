package com.saida.services.algorithm.resp;

import lombok.Data;

/**
 * 人脸对比 结果
 */
@Data
public class VlinkerAlgorithmFaceSearchResp {

    /**
     * 相似度 0-1
     */
    private Double similarity;

    /**
     * 人员id
     */
    private Long id;

    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别，1-男，2-女
     */
    private Integer sex;

    /**
     * 人员编号
     */
    private String number;

    /**
     * 人员照片，url逗号分隔
     */
    private String photos;

    /**
     * 匹配的图片
     */
    private String matchedPhoto;

}
