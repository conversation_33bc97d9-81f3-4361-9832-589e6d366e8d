package com.saida.services.deviceApi.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 语音通话超时获取响应
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonVoiceTimeoutResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 语音通话超时时间（秒）
     */
    @JSONField(name = "timeout")
    private Integer timeout;
}