package com.saida.services.gateway.util;

import org.springframework.web.server.ServerWebExchange;

import java.util.Objects;

public class IPUtil {

    public static String getClientIP(ServerWebExchange exchange) {
        String ipAddress = exchange.getRequest().getHeaders().getFirst("X-Forwarded-For");
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = exchange.getRequest().getHeaders().getFirst("X-Real-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = Objects.requireNonNull(exchange.getRequest().getRemoteAddress()).getAddress().getHostAddress();
        }
        return ipAddress;
    }
}
