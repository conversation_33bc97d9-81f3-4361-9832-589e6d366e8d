package com.saida.services.gateway.exception;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.gateway.util.ResponseUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

@Slf4j
@Configuration
public class GlobalExceptionHandler {
    @Bean
    @Order(-2)
    public ErrorWebExceptionHandler errorWebExceptionHandler() {
        return new CustomErrorWebExceptionHandler();
    }

    private static class CustomErrorWebExceptionHandler implements ErrorWebExceptionHandler {

        @Override
        public Mono<Void> handle(ServerWebExchange exchange, Throwable ex) {
            ServerHttpRequest request = exchange.getRequest();
            String path = request.getURI().getPath();
            if (ex instanceof BizRuntimeException) {
                BizRuntimeException cus = (BizRuntimeException) ex;
                log.error("拦截到ErrorWeb异常...path={},Exception={},code={},data={}", path, cus.getMessage(), cus.getCode(), cus.getError(), ex);
                return ResponseUtils.writeCustomErrorInfo(exchange.getResponse(), new JSONObject() {{
                    put("code", String.valueOf(cus.getCode()));
                    put("message", cus.getMessage());
                    put("error", cus.getMessage());
                    put("timestamp", DateUtil.now());
                    put("path", path);
                }}.toString());
            } else {
                log.error("拦截到ErrorWeb异常...msg={}", ex.getMessage(), ex);
            }
            return ResponseUtils.writeCustomErrorInfo(exchange.getResponse(), new JSONObject() {{
                put("code", "500");
                put("message", "系统升级中，请稍后");
                put("error", ex.getMessage());
                put("timestamp", DateUtil.now());
                put("path", path);
            }}.toString());
        }
    }
}
