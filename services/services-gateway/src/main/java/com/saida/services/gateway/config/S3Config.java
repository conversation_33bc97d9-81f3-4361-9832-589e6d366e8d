package com.saida.services.gateway.config;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "s3-data")
@ConditionalOnProperty(prefix = "s3-data", name = "enable", havingValue = "true")
public class S3Config {


    private Boolean enable;

    /**
     * 1 s3 2minio 3 Vclusters 4 Tianyi  5 aliyun 6 移动
     */
    private Integer type;

    /**
     * 区域代码，用于指定客户端请求的地理区域
     */
    private String region;

    /**
     * 访问密钥，用于身份验证和权限管理
     */
    private String accessKey;

    /**
     * 秘密密钥，与访问密钥一起使用以进行签名和验证
     */
    private String secretKey;

    /**
     * 服务终端地址，指定与之通信的服务器地址
     */
    private String endPoint;

    private String returnPoint;

    /**
     * 存储桶名称，用于存储和组织对象
     */
    private String bucket;

    private List<S3RuleConfig> rules;

    @Data
    public static class S3RuleConfig {
        /**
         * 过期时间（天），用于设置URL或凭证的有效期
         */
        private Integer expirationInDays;

        /**
         * 生命周期前缀
         */
        private String prefix;
    }


    @PostConstruct
    public void init() {
        log.info("开启了 S3Config: {}", this);
    }

}