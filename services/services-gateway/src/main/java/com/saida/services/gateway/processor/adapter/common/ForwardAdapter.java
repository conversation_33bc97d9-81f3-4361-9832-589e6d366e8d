package com.saida.services.gateway.processor.adapter.common;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.saida.services.gateway.processor.BaseAdapter;
import com.saida.services.gateway.processor.FrameModifyRequestBodyUtil;
import com.saida.services.gateway.processor.Processor;
import com.saida.services.gateway.processor.ProcessorContext;
import lombok.Getter;
import org.reactivestreams.Publisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;


/***
 * description: 转发后端核心适配器
 */
@Getter
@Component("forwardAdapter")
public class ForwardAdapter extends BaseAdapter {
    private static final Logger LOG = LoggerFactory.getLogger(ForwardAdapter.class);
    //因为是内部适配器，这里默认成全匹配即可，spring 网关 predicates 会做过滤转发到不同的路由
    @Value("${frame.gateway.ForwardAdapter:/**}")
    private String routeRule;

    private List<Processor> afterProcessors;

    private ObjectMapper objectMapper = new ObjectMapper();


    @Override
    public void process(ProcessorContext context) {
        Objects.requireNonNull(afterProcessors, "after processor is null");
        ServerWebExchange exchange = context.getExchange();
        ServerHttpResponse originalResponse = exchange.getResponse();
        DataBufferFactory bufferFactory = originalResponse.bufferFactory();
        ServerHttpResponseDecorator responseDecorator = new ServerHttpResponseDecorator(originalResponse) {
            @Override
            public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
                return super.writeWith(DataBufferUtils.join(Flux.from(body))
                        .map(dataBuffer -> {
                            byte[] content = new byte[dataBuffer.readableByteCount()];
                            dataBuffer.read(content);
                            DataBufferUtils.release(dataBuffer);
                            return content;
                        }).flatMap(bytes -> {
                            context.setData(bytes);
                            afterProcessors.forEach(p -> p.process(context));
                            return Mono.just(bufferFactory.wrap((byte[]) context.getData()));
                        }));
            }
            @Override
            public Mono<Void> writeAndFlushWith(Publisher<? extends Publisher<? extends DataBuffer>> body) {
                return writeWith(Flux.from(body).flatMapSequential(p -> p));
            }
        };

        //参数要排除value 为空的值，否则修改错误
        Map<String, Object> params = new HashMap<>();
        for (Map.Entry<String, Object> entry : context.getParameters().entrySet()) {
            if (entry.getValue() != null) {
                params.put(entry.getKey(), entry.getValue());
            }
        }
        Mono<Void> mono;
        if (context.isPenetrationRequest()) {//请求透传
            mono = context.getChain().filter(exchange.mutate().response(responseDecorator).build());
        } else {//参数再组装之后 传递
            mono = FrameModifyRequestBodyUtil.modifyRequestBodyByFromData(exchange.mutate().response(responseDecorator).build(),
                    context.getChain(), params);
        }
        context.setMono(mono);
    }


    @Override
    public void catchException(ProcessorContext context) {
        //前置出现异常后，这里进行后置处理
        afterProcessors.forEach(p -> p.process(context));
        context.delException();
        Mono<Void> mono = context.getExchange().getResponse().writeWith(Mono.just(context.getExchange().getResponse().bufferFactory().wrap((byte[]) context.getData())));
        context.setMono(mono);
    }

    public synchronized void setAfterProcessors(List<Processor> afterProcessors) {
        this.afterProcessors = afterProcessors;
    }

}
