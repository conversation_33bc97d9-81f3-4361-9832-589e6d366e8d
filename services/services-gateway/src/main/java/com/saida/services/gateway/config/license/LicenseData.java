package com.saida.services.gateway.config.license;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LicenseData {
    // 真正的签名
    private String signature;

    // 证书id 只是一个uuid
    private String licenseKey;
    //开始时间
    private String startDate;
    //结束时间
    private String endDate;
    //证书签发时间
    private Long createTime;
    //设备mac
    private String mac;

    // 以下参数暂定
    //视频验证
    private ConvData convData;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ConvData {
        //可以支持的设备新增数量限制
        private Integer deviceCount;
    }
}
