package com.saida.services.gateway.processor.adapter.safe;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.nimbusds.jose.JWSObject;
import com.nimbusds.jose.JWSVerifier;
import com.saida.services.constant.AuthConstants;
import com.saida.services.constant.RedisConstants;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.gateway.filter.endecrypt.GatewayContext;
import com.saida.services.gateway.processor.BaseAdapter;
import com.saida.services.gateway.processor.ProcessorContext;
import com.saida.services.gateway.result.ResultCode;
import com.saida.services.gateway.util.RedisUtil;
import com.saida.services.system.sys.entity.ClientAppEntity;
import com.saida.services.system.sys.pojo.JwtUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.Security;
import java.util.Base64;
import java.util.Map;

@Slf4j
@Component
public class InnerAppFilterAdapter extends BaseAdapter {

    private final String routeRule = "/*/inner/**";

    private static final PathMatcher pathMatcher = new AntPathMatcher();

    private static final String authorizationPrefix = "Basic ";

    @Autowired
    private JWSVerifier jwsVerifier;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public void process(ProcessorContext context) {
        ServerHttpRequest request = context.getExchange().getRequest();
        String path = request.getPath().pathWithinApplication().value();

        if (!pathMatcher.match(routeRule, path)) {
            return;
        }

        String authorization = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
        if (StringUtils.isEmpty(authorization)) {
            throw new BizRuntimeException(ResultCode.AUTHENTICATION_ERROR.getCode(), ResultCode.AUTHENTICATION_ERROR.getMsg());
        }
        if (authorization.startsWith(authorizationPrefix)) {
            return;
        }

        String timestamp = request.getHeaders().getFirst("timestamp");
        String signature = request.getHeaders().getFirst("signature");
        if (!NumberUtil.isNumber(timestamp)) {
            throw new BizRuntimeException(ResultCode.TIME_ERROR.getCode(), ResultCode.TIME_ERROR.getMsg());
        }
        if (Math.abs(System.currentTimeMillis() - Long.parseLong(timestamp)) > (300 * 1000L)) {
            throw new BizRuntimeException(ResultCode.TIME_SYNC_ERROR.getCode(), ResultCode.TIME_SYNC_ERROR.getMsg());
        }
        if (StringUtils.isEmpty(signature)) {
            throw new BizRuntimeException(ResultCode.SIGN_ERROR.getCode(), ResultCode.SIGN_ERROR.getMsg());
        }
        try {
            JWSObject jwsObject = JWSObject.parse(authorization);
            if (!jwsObject.verify(jwsVerifier)) {
                throw new BizRuntimeException(ResultCode.AUTHENTICATION_ERROR.getCode(), ResultCode.AUTHENTICATION_ERROR.getMsg());
            }
            Map<String, Object> map = jwsObject.getPayload().toJSONObject();
            Object jtiObj = map.get("jti");
            if (jtiObj == null) {
                throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), "认证信息错误");
            }
            String jti = jtiObj.toString();
            Object jwtUser = redisUtil.get(RedisConstants.CLIENT_APP_JWT + jti);
            if (jwtUser == null) {
                throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), ResultCode.TOKEN_INVALID_OR_EXPIRED.getMsg());
            }
            JwtUser user = JSONObject.toJavaObject(JSONObject.parseObject(jwtUser.toString()), JwtUser.class);

            if (user == null || StringUtils.isEmpty(user.getAccount())) {
                throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), ResultCode.TOKEN_INVALID_OR_EXPIRED.getMsg());
            }

            Object appObj = redisUtil.get(RedisConstants.CLIENT_APP + user.getAccount());
            if (appObj == null) {
                throw new BizRuntimeException(ResultCode.APP_ERROR.getCode(), ResultCode.APP_ERROR.getMsg());
            }
            ClientAppEntity clientApp = JSON.toJavaObject(JSON.parseObject(appObj.toString()), ClientAppEntity.class);
            if (clientApp == null) {
                throw new BizRuntimeException(ResultCode.APP_ERROR.getCode(), ResultCode.APP_ERROR.getMsg());
            }
            GatewayContext gatewayContext = GatewayContext.getGatewayContext(context.getExchange());
            gatewayContext.setUserId(user.getId());
            gatewayContext.setAccount(user.getAccount());
            gatewayContext.setUserName(user.getName());

            // 校验参数签名
            Map<String, Object> param = gatewayContext.getParam();
            String paramStr = "{}";
            if (param != null) {
                paramStr = JSON.toJSONString(param);
                paramStr = JSON.toJSONString(JSON.parseObject(paramStr), SerializerFeature.MapSortField);
            }

            String traceId = IdUtil.fastSimpleUUID();
            redisUtil.set(RedisConstants.TRACE_PARAM + traceId, paramStr, 60L);
            request = context.getExchange().getRequest().mutate()
                    .header(AuthConstants.JWT_PAYLOAD_KEY, URLEncoder.encode(jwtUser.toString(), "UTF-8"))
                    .build();
            context.setExchange(context.getExchange().mutate().request(request).build());
        } catch (Exception e) {
            log.error("msg={}", e.getMessage(), e);
            if (e instanceof BizRuntimeException) {
                BizRuntimeException ce = (BizRuntimeException) e;
                throw new BizRuntimeException(ce.getCode(), ce.getMessage());
            }
            throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), ResultCode.TOKEN_INVALID_OR_EXPIRED.getMsg());
        }
    }

    @Override
    public String getRouteRule() {
        return routeRule;
    }


    private static Cipher cipher;

    static {
        Security.addProvider(new BouncyCastleProvider());
        try {
            cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
        } catch (NoSuchAlgorithmException | NoSuchProviderException | NoSuchPaddingException e) {
            log.error("Cipher 创建失败！->", e);
        }
    }

    /**
     * 解密方法，使用AES算法解密给定的密文
     *
     * @param ciphertext 待解密的密文
     * @param secretKey  AES加密的密钥
     * @param iv         初始化向量
     * @return 解密后的明文
     */
    public static String decrypt(String ciphertext, String secretKey, String iv) {
        byte[] decode = new byte[0];
        try {
            // 创建AES密钥
            SecretKeySpec aes = new SecretKeySpec(secretKey.getBytes(), "AES");
            // 初始化Cipher对象
            cipher.init(Cipher.DECRYPT_MODE, aes, new IvParameterSpec(iv.getBytes()));
            // 将密文从Base64编码解码为字节数组
            decode = Base64.getDecoder().decode(ciphertext);
            // 使用Cipher对象对字节数组进行解密
            byte[] decryptedBytes = cipher.doFinal(decode);
            // 将解密后的字节数组转换为字符串并返回
            return new String(decryptedBytes);
        } catch (Exception e) {
            // 如果解密过程中出现异常，则记录错误日志
            log.error("decrypt 解密失败 ciphertext:{},secretKey:{},iv:{},decode:{} ->> msg={}", ciphertext, secretKey, iv, decode, e.getMessage(), e);
        }
        // 如果出现异常，则返回空字符串
        return "";
    }
}

