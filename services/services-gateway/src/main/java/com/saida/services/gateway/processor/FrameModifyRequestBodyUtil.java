package com.saida.services.gateway.processor;

import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 *
 */
public class FrameModifyRequestBodyUtil {

    public static Mono<Void> modifyRequestBodyByFromData(ServerWebExchange exchange, GatewayFilterChain chain, Map<String, Object> newBodyMap) {
        return chain.filter(exchange);
    }
}
