package com.saida.services.gateway.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.RandomUtil;
import com.ctq.csp.cspsdk.api.BaseApi;
import com.ctq.csp.cspsdk.api.SessionKeyApi;
import com.saida.services.entities.pojo.EncryptedData;
import com.saida.services.gateway.result.Result;
import org.springframework.web.bind.annotation.*;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/csp")
public class CspController {
    @GetMapping("init")
    public Result<Object> version(String uuid) {
        if (uuid == null || uuid.isEmpty()) {
            return Result.failed("uuid不能为空");
        }
        com.ctq.csp.cspsdk.domain.common.Result result = SessionKeyApi.sessionKeyApply(uuid);
        if (result.getCode() != 200) {
            return Result.success(result.getMsg());
        }
        return Result.success(result.getData());
    }

    @PostMapping("decrypt")
    public Result<String> decrypt(@RequestBody EncryptedData encryptedData) {
        return Result.success(SessionKeyApi.sessionKeyDecrypt(Base64.decodeStr(encryptedData.getSign())
                , encryptedData.getEncryptedData()
                , null
                , null
                , null
                , null));
    }


    public static void main(String[] args) {
        String str = "qteXL1RBuhtZxIz21sGWGwKcBYSki6jGg6H+KehqQW1mxhA+y35GVxFwpa0gpgB6";
        String key = "Zjc4MjM3Y2MzOGNlODFjMWMxY2Y0YjdjYmQxZGM5NTE=";
        BaseApi.init();
        String base64 = Base64.decodeStr(key);
        System.out.println(str);
        System.out.println("base64:" + base64);
//        String md5 = MD5.create().digestHex(key);
////        System.out.println("md5:" + md5);
        String s1 = SessionKeyApi.sessionKeyDecrypt(base64
                , str
                , null
                , null
                , null
                , null);
        System.out.println(s1);

        for (int i = 0; i < 100; i++) {
            String s2 = RandomUtil.randomString(47);
            System.out.println("原始：" + s2 + ",长度：" + s2.length());
            String s = SessionKeyApi.sessionKeyEncrypt(base64
                    , s2
                    , null
                    , null
                    , null
                    , null);
            System.out.println("加密后：" + s + ",长度：" + s.length());
            if (s.length() != 64){
                System.out.println("长度不一致");
            }
        }
    }
}
