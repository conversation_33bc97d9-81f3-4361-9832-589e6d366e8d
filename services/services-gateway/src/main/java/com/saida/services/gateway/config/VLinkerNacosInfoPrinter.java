package com.saida.services.gateway.config;

import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Slf4j
@Component
public class VLinkerNacosInfoPrinter {


    @Resource
    private NacosConfigProperties configProps;
    @Resource
    private NacosDiscoveryProperties discoveryProps;

    @PostConstruct
    public void nacosConfigInfoLog() {

        String serverAddr = configProps.getServerAddr();
        String namespace = configProps.getNamespace();
        for (int i = 0; i < 3; i++) {
            log.info("✅ ✅ ✅ Nacos Config -> 服务器地址: {} 命名空间:{}", serverAddr, namespace);
        }

        serverAddr = discoveryProps.getServerAddr();
        namespace = discoveryProps.getNamespace();
        for (int i = 0; i < 3; i++) {
            log.info("✅ ✅ ✅ Nacos Config -> 服务器地址: {} 命名空间:{} 注册自己的ip:{}", serverAddr, namespace, discoveryProps.getIp());
        }
    }
}
