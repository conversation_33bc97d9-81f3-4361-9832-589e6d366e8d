package com.saida.services.gateway.processor.adapter.common;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.saida.services.gateway.processor.BaseAdapter;
import com.saida.services.gateway.processor.ErrorCodeException;
import com.saida.services.gateway.processor.ProcessorContext;
import com.saida.services.gateway.processor.Utils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 *
*/
@Component("httpResponseAdapter")
public class HttpResponseAdapter  extends BaseAdapter {

    private static Logger LOG = LoggerFactory.getLogger(HttpDecoderAdapter.class);

    @Value("${frame.gateway.response.encode:UTF-8}")
    private String encoding;

    //多个逗号隔开
    @Value("${frame.gateway.response.header:}")
    private String header;

    @Value("${frame.gateway.HttpResponseAdapter:/**}")
    private  String  routeRule;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void process(ProcessorContext context) {
        try{
            processHeader(context);
            processData(context);
        }catch (Exception e){
            LOG.error("error.HttpResponse",e);
            throw new ErrorCodeException("error.HttpResponse");
        }

    }

    private void processHeader(ProcessorContext context) {
        ServerHttpResponse response = context.getExchange().getResponse();
        HttpHeaders headers = response.getHeaders();
        String contentType = headers.getFirst(HttpHeaders.CONTENT_TYPE);

        if(StringUtils.isEmpty(contentType)) {
            headers.set(HttpHeaders.CONTENT_TYPE,Utils.HTTP_CONTENT_TYPE_JSON);
        }

        if(StringUtils.isNotEmpty(header)){
            String[] hss = StringUtils.split(header,",");
            for(String hs : hss){
                String[] h = StringUtils.split(hs,"=");
                headers.set(h[0],h[1]);
            }
        }
    }

    // value
    private void processData(ProcessorContext context) throws Exception {
        Object bodyData = context.getData();
        if (bodyData != null) {
            if (bodyData instanceof String) {
                context.setData(((String) bodyData).getBytes(encoding));
            } else if (bodyData instanceof byte[]) {
                context.setData(bodyData);
            } else if (Map.class.isAssignableFrom(bodyData.getClass())) {
                Map<String, Object> resultMap  = (Map) bodyData;
                if(context.isOriginResponse() || resultMap.containsKey("data")){
//                    context.setData(JSON.toJSONString(bodyData).getBytes(encoding));
                    context.setData(objectMapper.writeValueAsString(bodyData).getBytes(encoding));
                    context.getExchange().getResponse().getHeaders().set(HttpHeaders.CONTENT_LENGTH,String.valueOf(((byte[])context.getData()).length));
                } else {
                    Map<String, Object> result = new HashMap<>();
                    result.put("data", bodyData);
                    result.put("success", Boolean.TRUE);
//                    context.setData(JSON.toJSONString(result).getBytes(encoding));
                    context.setData(objectMapper.writeValueAsString(result).getBytes(encoding));
                    context.getExchange().getResponse().getHeaders().set(HttpHeaders.CONTENT_LENGTH,String.valueOf(((byte[])context.getData()).length));
                }
            } else {
//                context.setData(JSON.toJSONString(bodyData).getBytes(encoding));
                context.setData(objectMapper.writeValueAsString(bodyData).getBytes(encoding));
            }
        }

    }

    @Override
    public String getRouteRule(){
        return routeRule;
    }


}
