package com.saida.services.gateway.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
@ConfigurationProperties("sysconfig")
public class SysConfig {
    private Map<String, String> staticResources = new LinkedHashMap<>();

    /**
     * 是否开启数据权限
     */
    private Boolean enableDataPermission = false;
    /**
     * 是否开启多租户
     */
    private Boolean enableTenant = false;

    /**
     * 是否校验租户合法
     */
    private Boolean enableCheckTenant = false;
}
