package com.saida.services.gateway.processor.adapter.safe;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.saida.services.constant.RedisConstants;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.gateway.processor.BaseAdapter;
import com.saida.services.gateway.processor.ProcessorContext;
import com.saida.services.gateway.util.IPUtil;
import com.saida.services.gateway.util.RedisUtil;
import com.saida.services.system.sys.entity.ClientAppBasicAuthEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import java.util.Arrays;

@Slf4j
@Component
public class AppBasicTokenFilterAdapter extends BaseAdapter {

    private final String routeRule = "/*/api/**";

    private static final String tokenPath = "/open-system/api/auth/token";

    private static final PathMatcher pathMatcher = new AntPathMatcher();

    private static final String authorizationPrefix = "Basic ";

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public void process(ProcessorContext context) {
        ServerHttpRequest request = context.getExchange().getRequest();
        String path = request.getPath().pathWithinApplication().value();

        if (!pathMatcher.match(routeRule, path) || tokenPath.equals(path)) {
            return;
        }

        String authorization = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
        if (StringUtils.isEmpty(authorization)) {
            throw new BizRuntimeException("授权错误");
        }
        if (!authorization.startsWith(authorizationPrefix)) {
            return;
        }
        String timestamp = request.getHeaders().getFirst("timestamp");
        if (StringUtils.isEmpty(timestamp) || !NumberUtil.isNumber(timestamp)) {
            throw new BizRuntimeException("时间错误");
        }
        if (Math.abs(System.currentTimeMillis() - Long.parseLong(timestamp)) > (300 * 1000L)) {
            throw new BizRuntimeException("时间错误");
        }
        authorization = StringUtils.substringAfter(authorization, authorizationPrefix);
        authorization = Base64.decodeStr(authorization);
        if (StringUtils.isEmpty(authorization)) {
            throw new BizRuntimeException("授权错误");
        }
        String[] userPass = authorization.split(":");
        if (userPass.length != 2) {
            throw new BizRuntimeException("授权错误");
        }
        Object value = redisUtil.get(String.format(RedisConstants.CLIENT_APP, userPass[0]));
        if (value == null) {
            throw new BizRuntimeException("授权错误");
        }
        ClientAppBasicAuthEntity clientApp = JSON.toJavaObject(JSON.parseObject(String.valueOf(value)), ClientAppBasicAuthEntity.class);
        if (clientApp == null) {
            throw new BizRuntimeException("授权错误");
        }
        String secure = SecureUtil.md5(String.format("%s:%s:%s", clientApp.getAppKey(), clientApp.getAppSecret(), timestamp));
        if (!secure.equals(userPass[1])) {
            throw new BizRuntimeException("授权错误");
        }
        String clientIp = IPUtil.getClientIP(context.getExchange());
        if (StringUtils.isEmpty(clientApp.getIpWhite()) || StringUtils.isEmpty(clientIp)) {
            throw new BizRuntimeException("IP错误");
        }
        String[] ipWhiteArray = clientApp.getIpWhite().split(",");
        log.info("ipWhite={}, 请求IP={}", JSON.toJSON(ipWhiteArray), clientIp);
        String tmpClientIp = Arrays.stream(ipWhiteArray).filter(ip -> ip.equals(clientIp)).findFirst().orElse(null);
        if (StringUtils.isEmpty(tmpClientIp)) {
            throw new BizRuntimeException("IP错误");
        }
        if (StringUtils.isEmpty(clientApp.getResource())) {
            throw new BizRuntimeException("资源未授权");
        }
        String tmpP = Arrays.stream(clientApp.getResource().split(",")).filter(p -> pathMatcher.match(p, path)).findFirst().orElse(null);
        if (StringUtils.isEmpty(tmpP)) {
            throw new BizRuntimeException("资源未授权");
        }
    }

    @Override
    public String getRouteRule() {
        return routeRule;
    }
}
