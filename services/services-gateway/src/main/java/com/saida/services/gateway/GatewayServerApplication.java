package com.saida.services.gateway;

import com.saida.services.tools.PrintUtil;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

@EnableFeignClients
@EnableDiscoveryClient
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class,
        DataSourceTransactionManagerAutoConfiguration.class,
        MongoAutoConfiguration.class,
        MongoDataAutoConfiguration.class})
public class GatewayServerApplication {

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(GatewayServerApplication.class);
        app.setBannerMode(Banner.Mode.OFF);
        app.setAddCommandLineProperties(false);
        ConfigurableApplicationContext configurableApplicationContext = app.run(args);

        Environment environment = configurableApplicationContext.getBean(Environment.class);
        PrintUtil.print(environment.getProperty("server.servlet.context-path"), environment.getProperty("server.port"));
    }
}