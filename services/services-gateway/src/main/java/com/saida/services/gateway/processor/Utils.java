package com.saida.services.gateway.processor;

import java.util.HashMap;
import java.util.Map;

public class Utils {
    public static final String ROUTE_PROTOCOL ="cover:";
    public static final String HTTP_CONTENT_TYPE_JSON = "application/json";

    public static final Map<String,String> routeMap = new HashMap<>();

    public static final String ERROR_CODE_ROUTE_NULL = "error.processor.route.null"; //路由配置为空


    //==========================================================属性配置


    //==========================================================前置以及后置拦截配置
    public static final String PROCESSOR_IN =  "${frame.gateway.processorIN:licenseFilterAdapter,tokenFilterAdapter,appTokenFilterAdapter,iotAppBasicTokenFilterAdapter,innerAppFilterAdapter,appBasicTokenFilterAdapter,httpDecoderAdapter,requestLogAdapter}";
    public static final String PROCESSOR_OUT = "${frame.gateway.processorOUT:forwardAfterAdapter}";
    //==========================================================前置以及后置拦截配置






}
