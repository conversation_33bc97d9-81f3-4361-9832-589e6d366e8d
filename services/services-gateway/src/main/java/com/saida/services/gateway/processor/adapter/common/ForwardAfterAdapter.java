package com.saida.services.gateway.processor.adapter.common;

import com.saida.services.gateway.processor.BaseAdapter;
import com.saida.services.gateway.processor.ProcessorContext;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;


/***
* @Description: 对转发后的返回报文进行解码，如果是异常报文，需要抛出进行后续异常处理
* @Author: dengkun
* @Date: 2023/6/1
*/
@Component("forwardAfterAdapter")
public class ForwardAfterAdapter extends BaseAdapter {

    @Value("${frame.gateway.response.encode:UTF-8}")
    private String encoding;

    @Getter
    @Value("${frame.gateway.ForwardAfterAdapter:/**}")
    private  String  routeRule;



    @Override
    public void process(ProcessorContext context) {
        MediaType contentType = context.getExchange().getResponse().getHeaders().getContentType();
        if(contentType == null){
            return;
        }
        String contentTypeHeader = contentType.toString();
        if(!contentTypeHeader.startsWith(MediaType.APPLICATION_JSON_VALUE) && !contentTypeHeader.startsWith(MediaType.APPLICATION_FORM_URLENCODED_VALUE)){
            return;
        }
        byte[] data = (byte[])context.getData();
        String body = new String(data, Charset.forName(encoding));
//        LOG.debug(">> 原始报文:{}",body);
        context.setData(body); //返回结果
    }

}
