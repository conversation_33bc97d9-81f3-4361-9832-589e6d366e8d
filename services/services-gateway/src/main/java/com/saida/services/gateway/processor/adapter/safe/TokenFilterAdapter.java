package com.saida.services.gateway.processor.adapter.safe;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWSObject;
import com.nimbusds.jose.JWSVerifier;
import com.saida.services.constant.AuthConstants;
import com.saida.services.constant.HeaderConstants;
import com.saida.services.constant.RedisConstants;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.gateway.filter.endecrypt.FilterUrlConfig;
import com.saida.services.gateway.filter.endecrypt.GatewayContext;
import com.saida.services.gateway.processor.BaseAdapter;
import com.saida.services.gateway.processor.ProcessorContext;
import com.saida.services.gateway.result.ResultCode;
import com.saida.services.gateway.util.RedisUtil;
import com.saida.services.system.sys.pojo.JwtUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class TokenFilterAdapter extends BaseAdapter {

    private static final PathMatcher pathMatcher = new AntPathMatcher();
    private final String routeRule = "/**";

    @Resource
    private FilterUrlConfig urlConfig;
    @Resource
    private JWSVerifier jwsVerifier;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public void process(ProcessorContext context) {
        ServerHttpRequest request = context.getExchange().getRequest();
        String path = request.getPath().pathWithinApplication().value();
        String authorization = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
        String customerUseragent = request.getHeaders().getFirst(HeaderConstants.CUSTOMER_USER_AGENT);

        if (StringUtils.isEmpty(authorization)) {
            authorization = request.getQueryParams().getFirst(HttpHeaders.AUTHORIZATION);
        }
        if (StringUtils.isEmpty(customerUseragent)) {
            customerUseragent = request.getQueryParams().getFirst(HeaderConstants.CUSTOMER_USER_AGENT);
        }
        if (urlConfig.getIgnoreUrls() != null && !urlConfig.getIgnoreUrls().isEmpty()) {
            String igUrl = urlConfig.getIgnoreUrls().stream().filter(u -> pathMatcher.match(u, path)).findFirst().orElse(null);
            if (!StringUtils.isEmpty(igUrl)) {
                return;
            }
        }
        try {
            if (StringUtils.isEmpty(authorization)) {
                throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), "认证信息错误");
            }
            JWSObject jwsObject = JWSObject.parse(authorization);
            if (!jwsObject.verify(jwsVerifier)) {
                throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), "认证信息错误");
            }
            Map<String, Object> map = jwsObject.getPayload().toJSONObject();

            // 获取`exp`声明
            Object expObj = map.get("exp");
            if (Objects.isNull(expObj)) {
                log.error("token过期时间错误.exp为null");
                throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), ResultCode.TOKEN_INVALID_OR_EXPIRED.getMsg());
            }
            if (expObj instanceof Number) {
                long exp = ((Number) expObj).longValue();
                if (DateUtil.currentSeconds() > exp) {
                    log.error("token过期...expObj:{}", expObj);
                    throw new BizRuntimeException(ResultCode.TOKEN_EXPIRED.getCode(), ResultCode.TOKEN_EXPIRED.getMsg());
                }
            } else {
                log.error("token过期时间错误...expObj:{}", expObj);
                throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), ResultCode.TOKEN_INVALID_OR_EXPIRED.getMsg());
            }

            Object jtiObj = map.get("jti");
            if (jtiObj == null) {
                throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), "认证信息错误");
            }
            String jti = jtiObj.toString();

            boolean srvSrvSystem = path.startsWith("/srv-system");
            Object jwtUser;
            if (srvSrvSystem && Objects.equals(HeaderConstants.MOBILE, customerUseragent)) {
                Object accountObj = map.get("account");
                if (accountObj == null) {
                    log.error("token account为空");
                    throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), "认证信息错误");
                }
                String account = accountObj.toString();

                jwtUser = redisUtil.get(RedisConstants.APP_USER_JWT + account);
            } else {
                jwtUser = redisUtil.get(RedisConstants.USER_JWT + jti);
            }
            if (jwtUser == null) {
                throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), "令牌无效");
            }
            JwtUser user = JSONObject.toJavaObject(JSONObject.parseObject(jwtUser.toString()), JwtUser.class);

            if (srvSrvSystem && Objects.equals(HeaderConstants.MOBILE, customerUseragent)) {
                if (!Objects.equals(user.getJti(), jti)) {
                    throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), "令牌无效");
                }
            }

            GatewayContext gatewayContext = GatewayContext.getGatewayContext(context.getExchange());
            gatewayContext.setUserId(user.getId());
            gatewayContext.setAccount(user.getAccount());
            gatewayContext.setUserName(user.getName());
            ServerHttpRequest.Builder mutate = context.getExchange().getRequest().mutate();
            HttpHeaders headers = request.getHeaders();
            headers.forEach((key, value) -> {
                mutate.header("x-" + key, value.get(0));
            });
            request = mutate.header(AuthConstants.JWT_PAYLOAD_KEY, URLEncoder.encode(jwtUser.toString(), "UTF-8"))
                    .build();
            context.setExchange(context.getExchange().mutate().request(request).build());
        } catch (BizRuntimeException e) {
            throw new BizRuntimeException(e.getCode(), e.getMessage());
        } catch (ParseException | JOSEException e) {
            throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), "认证信息错误");
        } catch (Exception e) {
            throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), e.getMessage());
        }
    }

    @Override
    public String getRouteRule() {
        return routeRule;
    }
}
