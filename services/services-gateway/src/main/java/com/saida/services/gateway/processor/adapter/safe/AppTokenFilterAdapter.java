package com.saida.services.gateway.processor.adapter.safe;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.nimbusds.jose.JWSObject;
import com.nimbusds.jose.JWSVerifier;
import com.saida.services.constant.AuthConstants;
import com.saida.services.constant.RedisConstants;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.gateway.filter.endecrypt.GatewayContext;
import com.saida.services.gateway.processor.BaseAdapter;
import com.saida.services.gateway.processor.ProcessorContext;
import com.saida.services.gateway.result.ResultCode;
import com.saida.services.gateway.util.RedisUtil;
import com.saida.services.system.sys.entity.CapabilityReleaseEntity;
import com.saida.services.system.sys.entity.ClientAppEntity;
import com.saida.services.system.sys.pojo.JwtUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.Security;
import java.util.Base64;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class AppTokenFilterAdapter extends BaseAdapter {

    private final String routeRule = "/*/api/**";

    private static final String tokenPath = "/open-system/api/auth/token";

    private static final PathMatcher pathMatcher = new AntPathMatcher();

    private static final String authorizationPrefix = "Basic ";

    /* APPID， AES */
//    private final Map<String, AESCBCPK7> appAesKeyMap = new HashMap<>();

    @Autowired
    private JWSVerifier jwsVerifier;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public void process(ProcessorContext context) {
        ServerHttpRequest request = context.getExchange().getRequest();
        String path = request.getPath().pathWithinApplication().value();

        if (!pathMatcher.match(routeRule, path) || tokenPath.equals(path)) {
            return;
        }

        String authorization = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
        if (StringUtils.isEmpty(authorization)) {
            throw new BizRuntimeException(ResultCode.AUTHENTICATION_ERROR.getCode(), ResultCode.AUTHENTICATION_ERROR.getMsg());
        }
        if (authorization.startsWith(authorizationPrefix)) {
            return;
        }

        String timestamp = request.getHeaders().getFirst("timestamp");
        String signature = request.getHeaders().getFirst("signature");
        if (!NumberUtil.isNumber(timestamp)) {
            throw new BizRuntimeException(ResultCode.TIME_ERROR.getCode(), ResultCode.TIME_ERROR.getMsg());
        }
        if (Math.abs(System.currentTimeMillis() - Long.parseLong(timestamp)) > (300 * 1000L)) {
            throw new BizRuntimeException(ResultCode.TIME_SYNC_ERROR.getCode(), ResultCode.TIME_SYNC_ERROR.getMsg());
        }
        if (StringUtils.isEmpty(signature)) {
            throw new BizRuntimeException(ResultCode.SIGN_ERROR.getCode(), ResultCode.SIGN_ERROR.getMsg());
        }
        try {
            JWSObject jwsObject = JWSObject.parse(authorization);
            if (!jwsObject.verify(jwsVerifier)) {
                log.error("jwsObject 验证失败 抛出无权限异常");
                throw new BizRuntimeException(ResultCode.AUTHENTICATION_ERROR.getCode(), ResultCode.AUTHENTICATION_ERROR.getMsg());
            }
            Map<String, Object> map = jwsObject.getPayload().toJSONObject();
            Object jtiObj = map.get("jti");
            if (jtiObj == null) {
                throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), "认证信息错误");
            }
            String jti = jtiObj.toString();
            Object jwtUser = redisUtil.get(RedisConstants.CLIENT_APP_JWT + jti);
            if (jwtUser == null) {
                log.error("jwtUser 不存在 抛出无权限异常 jti:{}", jti);
                throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), ResultCode.TOKEN_INVALID_OR_EXPIRED.getMsg());
            }
            JwtUser user = JSONObject.toJavaObject(JSONObject.parseObject(jwtUser.toString()), JwtUser.class);

            if (user == null || StringUtils.isEmpty(user.getAccount())) {
                log.error("用户信息不存在 抛出无权限异常 user:{}", user);
                throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), ResultCode.TOKEN_INVALID_OR_EXPIRED.getMsg());
            }
            log.info("user认证信息:{}", user);

            Object appObj = redisUtil.get(RedisConstants.CLIENT_APP + user.getAccount());
            if (appObj == null) {
                log.error("appObj 不存在 抛出无权限异常 user.getAccount:{}", user.getAccount());
                throw new BizRuntimeException(ResultCode.APP_ERROR.getCode(), ResultCode.APP_ERROR.getMsg());
            }
            log.info("appObj={}", JSON.toJSON(appObj));
            ClientAppEntity clientApp = JSON.toJavaObject(JSON.parseObject(appObj.toString()), ClientAppEntity.class);
            if (clientApp == null) {
                log.error("clientApp 不存在 抛出无权限异常");
                throw new BizRuntimeException(ResultCode.APP_ERROR.getCode(), ResultCode.APP_ERROR.getMsg());
            }
            if (!Objects.equals(clientApp.getStatus(), 1)) {
                log.error("clientApp状态禁用");
                throw new BizRuntimeException(ResultCode.APP_STATUS_ERROR.getCode(), ResultCode.APP_STATUS_ERROR.getMsg());
            }
            GatewayContext gatewayContext = GatewayContext.getGatewayContext(context.getExchange());
            gatewayContext.setUserId(user.getId());
            gatewayContext.setAccount(user.getAccount());
            gatewayContext.setUserName(user.getName());

            // 校验参数签名
            Map<String, Object> param = gatewayContext.getParam();
            String paramStr = "{}";
            if (param != null) {
                paramStr = JSON.toJSONString(param);
                paramStr = JSON.toJSONString(JSON.parseObject(paramStr), SerializerFeature.MapSortField);
            }

            log.info("clientApp={}", JSON.toJSON(clientApp));
            boolean flag = Objects.equals(clientApp.getInternal(), 1);
            log.info("flag={}", flag);
            // 如果是内部应用什么都不要去验证
            if (flag) {
                String traceId = IdUtil.fastSimpleUUID();
                redisUtil.set(RedisConstants.TRACE_PARAM + traceId, paramStr, 60L);
                request = context.getExchange().getRequest().mutate()
                        .header(AuthConstants.JWT_PAYLOAD_KEY, URLEncoder.encode(jwtUser.toString(), StandardCharsets.UTF_8))
                        .header("UserPlatformTypeUserId", request.getHeaders().getFirst("UserPlatformTypeUserId"))
                        .header("UserPlatformTypeId", clientApp.getAutoId() != null ? String.valueOf(clientApp.getAutoId()) : "55")
                        .header("UserPlatformType", request.getHeaders().getFirst("UserPlatformType"))
                        .build();
                context.setExchange(context.getExchange().mutate().request(request).build());
                return;
            }

            Object capabilityObj;
            if ("/open-system/api/videoBack/controlPlayback".equals(path)
                    || "/open-system/api/videoBack/stopPlayback".equals(path)
                    || "/open-system/api/videoBack/downloadPlayback".equals(path)
                    || "/open-system/api/videoBack/stopDownloadPlayback".equals(path)
                    || "/open-system/api/videoBack/downloadCloudPlaybackUrl".equals(path)
            ) {
                capabilityObj = redisUtil.get(RedisConstants.CAPABILITY_INFO + "/open-system/api/videoBack/getRecordTimeLine");
            } else {
                capabilityObj = redisUtil.get(RedisConstants.CAPABILITY_INFO + path);
            }
            if (capabilityObj == null) {
                log.error("capabilityObj 不存在 抛出无权限异常 path:{}", (RedisConstants.CAPABILITY_INFO + path));
                throw new BizRuntimeException(ResultCode.CAPABILITY_ERROR.getCode(), ResultCode.CAPABILITY_ERROR.getMsg());
            }
            CapabilityReleaseEntity capability = JSON.toJavaObject(JSON.parseObject(capabilityObj.toString()), CapabilityReleaseEntity.class);
            String format = String.format("AppCallTtotal:%s:%s:%s:%s", clientApp.getOrderId(), DateTime.now().toString("yyyy-MM-dd"), clientApp.getId(), capability.getId());
            Object totalObj = redisUtil.get(format);
            if (totalObj == null) {
                log.error("totalObj[{}] 不存在 抛出无权限异常", format);
                throw new BizRuntimeException(ResultCode.ACCESS_DENIED.getCode(), ResultCode.ACCESS_DENIED.getMsg());
            }
            Object orderExpireObj = redisUtil.get("OrderExpire:" + clientApp.getOrderId());
            if (orderExpireObj == null) {
                log.error("orderExpireObj[{}] 不存在 抛出无权限异常", clientApp.getOrderId());
                throw new BizRuntimeException(ResultCode.ORDER_INVALID.getCode(), ResultCode.ORDER_INVALID.getMsg());
            }
            String[] timeArr = orderExpireObj.toString().split("_");
            if (!DateUtil.isIn(DateTime.now(), DateUtil.parse(timeArr[0]), DateUtil.parse(timeArr[1]))) {
                log.error("订单已过期 抛出无权限异常");
                throw new BizRuntimeException(ResultCode.ORDER_EXPIRE.getCode(), ResultCode.ORDER_EXPIRE.getMsg());
            }
            int total = Integer.parseInt(String.valueOf(totalObj));
            String key = String.format(RedisConstants.OrderUseCount, clientApp.getOrderId(), capability.getId(), DateTime.now().toString("yyyy-MM-dd"));
            Object numObj = redisUtil.get(key);
            if (numObj != null) {
                int useNum = Integer.parseInt(String.valueOf(numObj));
                if (useNum >= total) {
                    log.error("调用次数已达上限 抛出无权限异常");
                    throw new BizRuntimeException(ResultCode.CALL_UPPER_LIMIT.getCode(), ResultCode.CALL_UPPER_LIMIT.getMsg());
                }
            }

//            String timestampStr = String.format("%016d", Long.parseLong(timestamp));
//            signature = decrypt(signature, clientApp.getAesKey(), timestampStr);
//            String calcSignature = SecureUtil.md5(String.format("%s%s", paramStr, timestamp));
//            if (!signature.equals(calcSignature)) {
//                log.error("签名不匹配 -> signature={}, calcSignature={}", signature, calcSignature);
//                throw new BizRuntimeException(ResultCode.SIGN_ERROR.getCode(), ResultCode.SIGN_ERROR.getMsg());
//            }

            String traceId = IdUtil.fastSimpleUUID();
            redisUtil.set(RedisConstants.TRACE_PARAM + traceId, paramStr, 60L);
            request = context.getExchange().getRequest().mutate()
                    .header(AuthConstants.JWT_PAYLOAD_KEY, URLEncoder.encode(jwtUser.toString(), StandardCharsets.UTF_8))
                    .build();
            context.setExchange(context.getExchange().mutate().request(request).build());
        } catch (Exception e) {
            log.error("msg={}", e.getMessage(), e);
            if (e instanceof BizRuntimeException) {
                BizRuntimeException ce = (BizRuntimeException) e;
                throw new BizRuntimeException(ce.getCode(), ce.getMessage());
            }
            throw new BizRuntimeException(ResultCode.TOKEN_INVALID_OR_EXPIRED.getCode(), ResultCode.TOKEN_INVALID_OR_EXPIRED.getMsg());
        }
    }

    @Override
    public String getRouteRule() {
        return routeRule;
    }


    private static Cipher cipher;

    static {
        Security.addProvider(new BouncyCastleProvider());
        try {
            cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
        } catch (NoSuchAlgorithmException | NoSuchProviderException | NoSuchPaddingException e) {
            log.error("Cipher 创建失败！->", e);
        }
    }

    public static String decrypt(String ciphertext, String secretKey, String iv) {
        byte[] decode = new byte[0];
        try {
            SecretKeySpec aes = new SecretKeySpec(secretKey.getBytes(), "AES");
            cipher.init(Cipher.DECRYPT_MODE, aes, new IvParameterSpec(iv.getBytes()));
            decode = Base64.getDecoder().decode(ciphertext);
            byte[] decryptedBytes = cipher.doFinal(decode);
            return new String(decryptedBytes);
        } catch (Exception e) {
            log.error("decrypt 解密失败 ciphertext:{},secretKey:{},iv:{},decode:{} ->> msg={}", ciphertext, secretKey, iv, decode, e.getMessage(), e);
        }
        return "";
    }
}

