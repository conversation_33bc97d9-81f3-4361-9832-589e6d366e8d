package com.saida.services.gateway.config.license;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Date;

@Getter
@Slf4j
@Component
public class LicenseConfig implements ApplicationListener<ApplicationReadyEvent> {

    private static final String LICENSE_PATH = "/data/apps/vlinker-api/license/license.key"; // 假设放在JAR同级目录
    private static final String PUBLIC_KEY_PATH = "license/public.key"; // 假设放在resources下
    // 项目路径占位符
    private static final String PROJECT_PATH = "PROJECT_PATH";

    private LicenseData licenseData;
    // 是否验证成功
    private Boolean authentication = false;
    // 在有效期内
    private Boolean validDate = false;

    @Value("${license.path:}")
    private String licenseKeyPath;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        try {
            // 项目启动路径
            String userDir = System.getProperty("user.dir");
            // 1. 加载 License 文件
            String licenseContent = null;
            try {
                licenseContent = new String(Files.readAllBytes(Paths.get(LICENSE_PATH)));
            } catch (IOException e) {
                log.error("默认路径读取 License 文件失败:");
            }
            if (licenseContent == null || licenseContent.isEmpty()) {
                // 从环境变量中读取 license文件
                String licensePath = System.getenv("VLINKER_LICENSE_PATH");
                if (StringUtils.isNotBlank(licensePath)) {
                    try {
                        licensePath = licensePath.replace(PROJECT_PATH, userDir);
                        licenseContent = new String(Files.readAllBytes(Paths.get(licensePath)));
                    } catch (IOException e) {
                        log.error("从环境变量中读取 License 文件失败:");
                    }
                }
            }
            if (licenseContent == null || licenseContent.isEmpty()) {
                // 从启动参数中读取 license文件
                String licensePath = System.getProperty("VLINKER_LICENSE_PATH");
                if (StringUtils.isNotBlank(licensePath)) {
                    try {
                        licensePath = licensePath.replace(PROJECT_PATH, userDir);
                        licenseContent = new String(Files.readAllBytes(Paths.get(licensePath)));
                    } catch (IOException e) {
                        log.error("从启动参数中读取 License 文件失败:");
                    }
                }
            }
            if (licenseContent == null || licenseContent.isEmpty()) {
                // 从启动指令里面找 遍历 args 查找 --LICENSE_PATH
                String licensePath = null;
                for (String arg : event.getArgs()) {
                    if (arg.startsWith("--VLINKER_LICENSE_PATH=")) {
                        licensePath = arg.substring("--VLINKER_LICENSE_PATH=".length());
                        break;
                    }
                }
                if (StringUtils.isNotBlank(licensePath)) {
                    try {
                        licensePath = licensePath.replace(PROJECT_PATH, userDir);
                        licenseContent = new String(Files.readAllBytes(Paths.get(licensePath)));
                    } catch (IOException e) {
                        log.error("从启动指令中读取 License 文件失败:");
                    }
                }
            }
            if (StringUtils.isEmpty(licenseContent)) {
                if (StringUtils.isNotEmpty(licenseKeyPath)) {
                    try {
                        licenseKeyPath = licenseKeyPath.replace(PROJECT_PATH, userDir);
                        licenseContent = new String(Files.readAllBytes(Paths.get(licenseKeyPath)));
                    } catch (IOException e) {
                        log.error("从配置文件中读取 License 文件失败...msg={}", e.getMessage(), e);
                    }
                }
            }
            if (licenseContent == null || licenseContent.isEmpty()) {
                return;
            }
            ObjectMapper mapper = new ObjectMapper();
            licenseData = mapper.readValue(licenseContent, LicenseData.class);
            String publicKeyContent;
            // 2. 加载公钥
            try {
                ClassPathResource resource = new ClassPathResource(PUBLIC_KEY_PATH);
                try (InputStream inputStream = resource.getInputStream();
                     ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, length);
                    }
                    publicKeyContent = outputStream.toString(StandardCharsets.UTF_8.name());
                }
            } catch (IOException e) {
                for (int i = 0; i < 10; i++) {
                    log.error("公钥加载失败！");
                }
                log.error("公钥加载失败！", e);
                return;
            }
            PublicKey publicKey = loadPublicKey(publicKeyContent);

            LicenseData temp = new LicenseData();
            BeanUtils.copyProperties(licenseData, temp);
            temp.setSignature(null);

            // 3. 验证签名
            String jsonWithoutSignature = mapper.writeValueAsString(temp);
            if (!verifySignature(jsonWithoutSignature, licenseData.getSignature(), publicKey)) {
                for (int i = 0; i < 10; i++) {
                    log.error("授权证书认证失败！请联系技术人员");
                }
                return;
            }
            authentication = true;
            // 4. 检查有效期
            if (isExpired(licenseData)) {
                for (int i = 0; i < 10; i++) {
                    log.error("授权证书已过期！请联系技术人员");
                }
                return;
            }
            validDate = true;
            log.info("授权证书认证成功.证书有效期至{}", licenseData.getEndDate());
        } catch (Exception e) {
            for (int i = 0; i < 10; i++) {
                log.error("授权证书认证失败！请联系开发人员");
            }
            log.error("授权证书认证失败: {}", e.getMessage(), e);
        }
    }

    private PublicKey loadPublicKey(String keyContent) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(keyContent);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        return kf.generatePublic(spec);
    }

    private boolean verifySignature(String data, String signature, PublicKey publicKey) throws Exception {
        Signature sig = Signature.getInstance("SHA256withRSA");
        sig.initVerify(publicKey);
        sig.update(data.getBytes(StandardCharsets.UTF_8));
        return sig.verify(Base64.getDecoder().decode(signature));
    }

    private boolean isExpired(LicenseData license) {
        // 这里可以添加有效期检查逻辑，例如比较当前日期
        Date date = DateUtil.parse(license.getEndDate(), "yyyy-MM-dd");
        return DateUtil.compare(new Date(), date) > 0;
    }
}
