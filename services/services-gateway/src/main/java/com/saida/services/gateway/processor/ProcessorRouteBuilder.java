package com.saida.services.gateway.processor;

import com.saida.services.gateway.processor.adapter.common.ForwardAdapter;
import com.saida.services.gateway.processor.adapter.common.HttpResponseAdapter;
import com.saida.services.gateway.processor.adapter.safe.TokenFilterAdapter;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.saida.services.gateway.processor.Utils.PROCESSOR_IN;
import static com.saida.services.gateway.processor.Utils.PROCESSOR_OUT;

@Slf4j
@Aspect
@Component
public class ProcessorRouteBuilder {
    @Value(PROCESSOR_IN)
    private String processorIN;
    @Value(PROCESSOR_OUT)
    private String processorOUT;
    @Autowired
    private ApplicationContext applicationContext;
    private final List<Processor> pipeline = new ArrayList<>();
    @Autowired
    private ForwardAdapter forwardAdapter; //转发处理器
    @Autowired
    private HttpResponseAdapter responseAdapter;

    @Setter
    @Getter
    private Map<String, PathMatcher> pathMap = new ConcurrentHashMap<>();

    private final PathMatcher pathMatcher = new AntPathMatcher();

    @PostConstruct
    public void init() {
        Objects.requireNonNull(processorIN, "gateway processorIN is null.");
        Objects.requireNonNull(processorOUT, "gateway processorOUT is null.");
        initPipeLine();
    }


    private void initPipeLine() {

        log.info("=====================适配器初始化开始=====================");
        //前置
        initBeforePipe();

        //后置
        initAfterPipe();

        pipeline.add(forwardAdapter);
        log.info("=====================适配器初始化结束=====================");

    }

    private void initAfterPipe() {
        List<Processor> processorOuts = new ArrayList<>();
        Arrays.stream(processorOUT.split(",")).forEach(s -> {
            if (StringUtils.isNotEmpty(s)) {
                Processor p = (Processor) applicationContext.getBean(s.trim());
                log.info(">> initAfterPipe 加入适配器:[{}]", s);
                processorOuts.add(p);
            }
        });

        processorOuts.add(responseAdapter);
        forwardAdapter.setAfterProcessors(processorOuts);
    }

    private void initBeforePipe() {
        Arrays.stream(processorIN.split(",")).forEach(s -> {
            if (StringUtils.isNotEmpty(s)) {
                Processor p = (Processor) applicationContext.getBean(s.trim());
                log.info(">> initBeforePipe 加入适配器:[{}]", s);
                pipeline.add(p);
            }
        });
    }


    public Mono<Void> apply(ServerWebExchange exchange, GatewayFilterChain chain) {
        ProcessorContext context = new ProcessorContext(exchange, chain);
        context.setPenetrationRequest(true);//暂且默认透传 不对参数报文做处理
        for (Processor p : pipeline) {
            p.process(context);
        }
        if (context.hasException()) {
            forwardAdapter.catchException(context);
        }
        return context.getMono();
    }


    @Resource
    private TokenFilterAdapter tokenFilterAdapter;

    public Mono<Void> applyToSse(ServerWebExchange exchange, GatewayFilterChain chain) {
        ProcessorContext context = new ProcessorContext(exchange, chain);
        context.setPenetrationRequest(true);//暂且默认透传 不对参数报文做处理
        tokenFilterAdapter.process(context);
        Mono<Void> mono = context.getChain().filter(context.getExchange());
        context.setMono(mono);
        if (context.hasException()) {
            forwardAdapter.catchException(context);
        }
        return context.getMono();
    }


    @Pointcut("execution(public * com.saida.services.gateway.processor..*.*Adapter.*(..))")
    public void pointCut() {
    }

    @Around(value = "pointCut()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        Object target = pjp.getTarget();

        Signature signature = pjp.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        String methodName = method.getName();


        if (!(target instanceof Processor)
            || !"process".equals(methodName)
               && !"catchException".equals(methodName)) {
            return pjp.proceed();
        }

        Object[] args = pjp.getArgs();
        ProcessorContext context = (ProcessorContext) args[0];
        Processor processor = (Processor) target;

        //改变执行逻辑，出现异常后执行 各个适配器执行后续catchException流程
        try {
            String name = processor.getClass().getName();
            String routeId = context.getRouteId();
            //对路由进行判断是否执行
            if (!checkProcessRoute(name, processor.getRouteRule(), routeId)) {
                return null;
            }
            if (context.hasException()) {
                try {
                    processor.catchException(context);
                } catch (Exception e) {
                    context.attachException(e);
                }
            } else {
                try {
                    processor.process(context);
                } catch (Exception e) {
                    context.attachException(e);
                    throw e;
                }
            }
        } catch (Exception e) {
            if (e.getCause() != null) {
                context.attachException((Exception) e.getCause());
            }
            throw e;
        }
        return null;
    }


    private boolean checkProcessRoute(String processorName, String routeRule, String routeId) {
        return checkProcessRoute0(processorName, routeRule, routeId);
    }

    // true 符合路由
    private boolean checkProcessRoute0(String processorName, String routeRule, String routeId) {
        // 1. 默认代码内置的 2.配置文件读取的
        String calRoute = StringUtils.defaultIfEmpty(Utils.routeMap.get(processorName), "");
        if (StringUtils.isEmpty(calRoute) && StringUtils.isEmpty(routeRule)) {
            throw new ErrorCodeException(Utils.ERROR_CODE_ROUTE_NULL);
        }
        if (StringUtils.isNotEmpty(routeRule)) {
            //存在则不使用默认代码内置的路由
            boolean cover = routeRule.startsWith(Utils.ROUTE_PROTOCOL);
            calRoute = cover
                    ? routeRule.substring(Utils.ROUTE_PROTOCOL.length())//覆盖的方式
                    : calRoute + "," + routeRule; //采用累加的方式
        }
        for (String r : calRoute.split(",")) {
            if (StringUtils.isEmpty(r)) {
                continue;
            }
            if (r.startsWith("!")) {
                if (pathMatcher.match(r.substring(1), routeId)) {
                    return false;
                }
            } else {
                if (pathMatcher.match(r, routeId)) {
                    return true;
                }
            }
        }
        return false;
    }

}
