package com.saida.services.gateway.filter;

import com.saida.services.gateway.processor.ProcessorRouteBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.filter.NettyWriteResponseFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;


/**
 * 全局过滤器统一入口，注意order 一定要小于GlobalResponseBodyEncodeFilter -1 否则会导致response重写不生效
 */
@Slf4j
@Component
public class GateWayGlobalFilter implements GlobalFilter, Ordered {

    @Resource
    private ProcessorRouteBuilder routeBuilder;


    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // 检查是否是SSE请求
        if (isSseRequest(exchange)) {
            // 如果是SSE请求，则跳过此过滤器，直接调用链中的下一个过滤器
            log.info("收到当前请求为sse请求{}！", exchange.getRequest().getPath().value());
            return exchange.getSession().flatMap(webSession -> routeBuilder.applyToSse(exchange, chain));
        }
        return exchange.getSession().flatMap(webSession -> routeBuilder.apply(exchange, chain));
    }

    @Override
    public int getOrder() {
        return NettyWriteResponseFilter.WRITE_RESPONSE_FILTER_ORDER - 5;
    }

    private boolean isSseRequest(ServerWebExchange exchange) {
        // 实现检查是否是SSE请求的逻辑，例如检查请求头中的Accept字段
        String acceptHeader = exchange.getRequest().getHeaders().getFirst(HttpHeaders.ACCEPT);
        String contentTypeHeader = exchange.getRequest().getHeaders().getFirst(HttpHeaders.CONTENT_TYPE);
        return "text/event-stream" .equals(acceptHeader) || "text/event-stream" .equals(contentTypeHeader);
    }
}
