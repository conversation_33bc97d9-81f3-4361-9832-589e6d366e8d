package com.saida.services.gateway.processor.adapter.safe;

import com.saida.services.gateway.config.license.LicenseConfig;
import com.saida.services.gateway.processor.BaseAdapter;
import com.saida.services.gateway.processor.ProcessorContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class LicenseFilterAdapter extends BaseAdapter {

    @Resource
    private LicenseConfig licenseConfig;

    @Override
    public void process(ProcessorContext context) {
//        if (!licenseConfig.getAuthentication()) {
//            throw new BizRuntimeException("授权信息错误，请联系技术人员");
//        }
//        if (!licenseConfig.getValidDate()) {
//            throw new BizRuntimeException("授权信息已过期，请联系技术人员");
//        }
//        context.setExchange(context.getExchange().mutate().build());
    }

    @Override
    public String getRouteRule() {
        return "/**";
    }
}
