package com.saida.services.gateway.processor.adapter.safe;

import com.alibaba.fastjson.JSONObject;
import com.saida.services.constant.AuthConstants;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.gateway.processor.BaseAdapter;
import com.saida.services.gateway.processor.ProcessorContext;
import com.saida.services.gateway.result.ResultCode;
import com.saida.services.gateway.util.RedisUtil;
import com.saida.services.iot.constant.RedisKeyConstant;
import com.saida.services.iot.dto.ApplicationRedisDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Optional;

@Slf4j
@Component
public class IotAppBasicTokenFilterAdapter extends BaseAdapter {

    @Resource
    private RedisUtil redisUtil;

    private final String routeRule = "/*/iotApi/**";
    private static final PathMatcher pathMatcher = new AntPathMatcher();

    private static final String APP_CODE = "appcode";
    private static final String APP_SECRET = "appsecret";

    @Override
    public void process(ProcessorContext context) {
        ServerHttpRequest request = context.getExchange().getRequest();
        String path = request.getPath().pathWithinApplication().value();
        if (!pathMatcher.match(routeRule, path)) {
            return;
        }

        HttpHeaders headers = request.getHeaders();
        String appCode = headers.getFirst(APP_CODE);
        String appSecret = headers.getFirst(APP_SECRET);
        if (StringUtils.isEmpty(appCode) || StringUtils.isEmpty(appSecret)) {
            throw new BizRuntimeException(ResultCode.AUTHENTICATION_ERROR.getCode(), "认证信息错误");
        }
        Object authValueObject = redisUtil.get(RedisKeyConstant.APP_REDIS_KEY_PREFIX + appCode + ":" + appSecret);
        if (null == authValueObject || org.springframework.util.StringUtils.isEmpty(authValueObject.toString())) {
            log.error("认证信息错误 appCode:{},appSecret:{}", appCode, appSecret);
            throw new BizRuntimeException(ResultCode.AUTHENTICATION_ERROR.getCode(), "认证信息错误");
        }
        String authValue = authValueObject.toString();
        ApplicationRedisDto applicationRedisDto = null;
        try {
            applicationRedisDto = JSONObject.parseObject(authValue, ApplicationRedisDto.class);
        } catch (Exception e) {
            log.error("认证信息错误,appCode:{}, authValue:{}",appCode, authValue, e);
            throw new BizRuntimeException(ResultCode.AUTHENTICATION_ERROR.getCode(), "认证信息错误");
        }
        String[] split = applicationRedisDto.getApiAuth().split(",");
        // Optional<String> first = Arrays.stream(split).filter(u -> pathMatcher.match(u, path)).findFirst();
        Optional<String> first = Arrays.stream(split).filter(path::endsWith).findFirst();
        if (!first.isPresent()) {
            log.error("无此接口权限->appCode:{}path:{},apiAuth:{}",appCode, path, applicationRedisDto.getApiAuth());
            throw new BizRuntimeException(ResultCode.AUTHENTICATION_ERROR.getCode(), "无此接口权限");
        }
        request = context.getExchange().getRequest().mutate()
                .header(AuthConstants.APP_ID, String.valueOf(applicationRedisDto.getId()))
                .build();
        context.setExchange(context.getExchange().mutate().request(request).build());
    }

    @Override
    public String getRouteRule() {
        return routeRule;
    }
}
