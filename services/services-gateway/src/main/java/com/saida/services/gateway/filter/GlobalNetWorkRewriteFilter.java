package com.saida.services.gateway.filter;

import com.alibaba.fastjson2.JSON;
import com.saida.services.gateway.config.NetWorkConfig;
import com.saida.services.gateway.config.S3Config;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.filter.NettyWriteResponseFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GlobalNetWorkRewriteFilter implements GlobalFilter, Ordered {

    @Autowired(required = false)
    private NetWorkConfig netWorkConfig;
    @Autowired(required = false)
    private S3Config s3Config;
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // 配置检查
        if (netWorkConfig == null || s3Config == null) {
            return chain.filter(exchange);
        }
        if (!Boolean.TRUE.equals(s3Config.getEnable()) || !Boolean.TRUE.equals(netWorkConfig.getEnable())) {
            return chain.filter(exchange);
        }

        String refererHeader = exchange.getRequest().getHeaders().getFirst("Referer");
        if (StringUtils.isBlank(refererHeader)) {
            return chain.filter(exchange);
        }

        // 获取所有匹配的规则
        List<NetWorkConfig.NetWorkData> matchedList = netWorkConfig.getHost().stream()
                .filter(item -> StringUtils.isNotBlank(item.getType()) &&
                        StringUtils.isNotBlank(item.getReferer()) &&
                        refererHeader.startsWith(item.getReferer()))
                .collect(Collectors.toList());

        if (matchedList.isEmpty()) {
            return chain.filter(exchange);
        }

        log.info("匹配到网关转发规则：refererHeader:{} => netWorkConfig:{}", refererHeader, JSON.toJSONString(matchedList));

        ServerHttpResponse originalResponse = exchange.getResponse();
        DataBufferFactory bufferFactory = originalResponse.bufferFactory();
        HttpHeaders headers = originalResponse.getHeaders();

        ServerHttpResponseDecorator decoratedResponse = new ServerHttpResponseDecorator(originalResponse) {
            @Override
            @NonNull
            public Mono<Void> writeWith(@NonNull Publisher<? extends DataBuffer> body) {
                if (!(body instanceof Flux)) {
                    return super.writeWith(body);
                }
                MediaType contentType = headers.getContentType();
                if (contentType == null || (!MediaType.APPLICATION_JSON.isCompatibleWith(contentType)
                        && !MediaType.TEXT_PLAIN.isCompatibleWith(contentType))) {
                    return super.writeWith(body);
                }
                // 使用 DataBufferUtils.join 合并所有 buffer
                return DataBufferUtils.join(body)
                        .flatMap(fullBuffer -> {
                            try {
                                byte[] contentBytes = new byte[fullBuffer.readableByteCount()];
                                fullBuffer.read(contentBytes);
                                String bodyStr = new String(contentBytes, StandardCharsets.UTF_8);

                                // 遍历替换
                                for (NetWorkConfig.NetWorkData config : matchedList) {
                                    String type = config.getType();
                                    if ("s3".equalsIgnoreCase(type)) {
                                        if (s3Config.getReturnPoint() != null && config.getToUrl() != null) {
                                            bodyStr = bodyStr.replace(s3Config.getReturnPoint(), config.getToUrl());
                                        }
                                    } else if ("txt".equalsIgnoreCase(type)) {
                                        if (config.getFromUrl() != null && config.getToUrl() != null) {
                                            bodyStr = bodyStr.replace(config.getFromUrl(), config.getToUrl());
                                        }
                                    }
                                }

                                byte[] newContent = bodyStr.getBytes(StandardCharsets.UTF_8);
                                DataBuffer newBuffer = bufferFactory.wrap(newContent);
                                headers.setContentLength(newContent.length);
                                // 最终调用 super.writeWith 返回 Mono<Void>
                                return super.writeWith(Mono.just(newBuffer));
                            } finally {
                                DataBufferUtils.release(fullBuffer);
                            }
                        });
            }
        };


        // 替换响应对象，继续过滤链
        return chain.filter(exchange.mutate().response(decoratedResponse).build());
    }

    @Override
    public int getOrder() {
        return NettyWriteResponseFilter.WRITE_RESPONSE_FILTER_ORDER;
    }
}