package com.saida.services.gateway.processor;

import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/***
 */
@Data
public class ProcessorContext {
    private static final Logger logger = LoggerFactory.getLogger(ProcessorContext.class);
    private ServerWebExchange exchange;
    private GatewayFilterChain chain;
    private Mono<Void> mono;
    private String routeId;
    private Object data; // response data
    private boolean originResponse;
    private List<Exception> causes = new ArrayList<>();
    private Map<String, Object> parameters = new HashMap<>(); //参数
    private Map<String, Object> cMap = new HashMap<>(); //其他缓存变量

    private boolean penetrationRequest; //透传请求

    public ProcessorContext(ServerWebExchange exchange, GatewayFilterChain chain) {
        this.exchange = exchange;
        this.chain = chain;
        this.routeId = exchange.getRequest().getURI().getPath();
    }

    public void store(String k, Object v) {
        this.cMap.put(k, v);
    }

    public <T> T load(String k) {
        return (T) this.cMap.get(k);
    }


    public boolean hasException() {
        return !causes.isEmpty();
    }

    public Exception getException() {
        return hasException() ? causes.get(0) : null;
    }

    public void delException() {
        causes.clear();
    }

    public String getExceptionMessage() {
        String message = "";
        if (hasException()) {
            message = getException().getMessage();
            if (StringUtils.isEmpty(message)) {
                message = getException().getClass().getName();
            }
        }
        return message;
    }


    public void attachException(Exception e) {
        causes.add(e);
    }
}
