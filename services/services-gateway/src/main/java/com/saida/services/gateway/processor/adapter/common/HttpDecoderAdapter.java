package com.saida.services.gateway.processor.adapter.common;

import com.saida.services.gateway.filter.endecrypt.GatewayContext;
import com.saida.services.gateway.processor.BaseAdapter;
import com.saida.services.gateway.processor.ProcessorContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import java.util.HashMap;


/***
* Description: 将请求参数构造成MAP 方便后续适配器处理(POST/GET参数处理)
*/
@Slf4j
@Component("httpDecoderAdapter")
public class HttpDecoderAdapter extends BaseAdapter {


    @Value("${frame.gateway.HttpDecoderAdapter:/**}")
    private String routeRule;


    @Override
    public void process(ProcessorContext context) {
        ServerWebExchange change = context.getExchange();
        GatewayContext gatewayContext = change.getAttribute(GatewayContext.CACHE_GATEWAY_CONTEXT);
        context.setParameters(new HashMap<>());
        if (gatewayContext != null && gatewayContext.getParam() != null) {
            context.setParameters(gatewayContext.getParam());
        } else {
            log.warn(">> 获取参数解析上下文为空");
        }
    }

    @Override
    public String getRouteRule() {
        return routeRule;
    }
}
