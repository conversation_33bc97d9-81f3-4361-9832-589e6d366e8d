package com.saida.services.gateway.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

@Slf4j
@Data
@Component
@ConfigurationProperties("network")
@ConditionalOnProperty(prefix = "network", name = "enable", havingValue = "true")
public class NetWorkConfig {


    private Boolean enable;

    private List<NetWorkData> host;


    @Data
    public static class NetWorkData {
        /**
         * 可选项
         * s3  存储转发
         * txt  文本直接替换
         */
        private String type;
        private String referer;
        private String fromUrl;
        private String toUrl;
    }


    @PostConstruct
    public void init() {
        log.info("开启了 NetWorkConfig: {}", this);
    }
}
