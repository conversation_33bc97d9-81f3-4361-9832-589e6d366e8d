package com.saida.services.gateway.config;

import com.ctq.csp.cspsdk.api.BaseApi;
import com.ctq.csp.cspsdk.api.SessionKeyApi;
import com.saida.services.constant.VarConstants;
import com.saida.services.entities.pojo.EncryptedData;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.gateway.result.ResultCode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
@Component
@ConfigurationProperties("csp.config")
@ConditionalOnProperty(prefix = "csp.config", name = "enable", havingValue = "true")
public class CspConfig {

    private String keyId;

    private String enable;

    @PostConstruct
    public void init() {
        log.info("csp.config.enable:{}", enable);
        if ("true".equals(enable)) {
            new Thread(BaseApi::init);
        }
    }

    public static void main(String[] args) {
        BaseApi.init();

    }

    public String encryptResp(String data, String cspSign) throws BizRuntimeException {
        log.info("量子解密 data:{},cspSign:{}", data, cspSign);
        return SessionKeyApi.sessionKeyEncrypt(cspSign
                , data
                , null
                , null
                , null
                , null);
    }


    public String decryptReq(Map<String, List<String>> formdata) throws BizRuntimeException {
        if (formdata.get("encryptedData").get(0) == null || formdata.get("encryptedData").get(0).isEmpty()) {
            throw new BizRuntimeException(ResultCode.BAD_REQUEST_PARAM.getCode(), "缺少必要参数!");
        }
        if (formdata.get("timestamp").get(0) == null || formdata.get("timestamp").get(0).isEmpty()) {
            throw new BizRuntimeException(ResultCode.BAD_REQUEST_PARAM.getCode(), "缺少必要参数!");
        }
        if (formdata.get("sign").get(0) == null || formdata.get("sign").get(0).isEmpty()) {
            throw new BizRuntimeException(ResultCode.BAD_REQUEST_PARAM.getCode(), "缺少必要参数!");
        }
        String encryptedData = formdata.get("encryptedData").get(0);
        String timestampstr = formdata.get("timestamp").get(0);
        String sign = formdata.get("sign").get(0);
        long timestamp = Long.parseLong(timestampstr);
        if (((System.currentTimeMillis() - timestamp) / 1000) > VarConstants.TIMESTAMP_EXPIRE) {
            throw new BizRuntimeException("时间校验错误! 请保持和服务器同步");
        }
        EncryptedData data = new EncryptedData();
        data.setEncryptedData(encryptedData);
        data.setSign(sign);
        data.setTimestamp(timestampstr);
        return decryptReq(data);
    }


    public String decryptReq(EncryptedData formdata) throws BizRuntimeException {
        if (formdata == null) {
            throw new BizRuntimeException(ResultCode.BAD_REQUEST_PARAM.getCode(), "缺少必要参数!");
        }
        if (org.apache.commons.lang.StringUtils.isEmpty(formdata.getTimestamp())) {
            throw new BizRuntimeException(ResultCode.BAD_REQUEST_PARAM.getCode(), "缺少必要参数!");
        }
        if (org.apache.commons.lang.StringUtils.isEmpty(formdata.getEncryptedData())) {
            throw new BizRuntimeException(ResultCode.BAD_REQUEST_PARAM.getCode(), "缺少必要参数!");
        }
        if (org.apache.commons.lang.StringUtils.isEmpty(formdata.getSign())) {
            throw new BizRuntimeException(ResultCode.BAD_REQUEST_PARAM.getCode(), "缺少必要参数!");
        }
        long timestamp = Long.parseLong(formdata.getTimestamp());
        if (((System.currentTimeMillis() - timestamp) / 1000) > VarConstants.TIMESTAMP_EXPIRE) {
            throw new BizRuntimeException("时间校验错误! 请保持和服务器同步");
        }
        log.info("解密请求参数encryptedData:{},sign;{}", formdata.getEncryptedData(), formdata.getSign());
        String s = SessionKeyApi.sessionKeyDecrypt(formdata.getSign()
                , formdata.getEncryptedData()
                , null
                , null
                , null
                , null);
        log.info("解密请求参数s:{}", s);
        return s;
    }
}
