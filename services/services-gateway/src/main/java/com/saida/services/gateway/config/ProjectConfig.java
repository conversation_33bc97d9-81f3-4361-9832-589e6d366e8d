package com.saida.services.gateway.config;

import cn.hutool.core.date.DateUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Data
@Component
@ConfigurationProperties("project")
public class ProjectConfig {

    /**
     */
    private String name = "";
    /**
     */
    private String version = "";

    private String versionTime = "";

    private String startTime = "";


    @PostConstruct
    public void init() {
        startTime = DateUtil.now();
    }


}
