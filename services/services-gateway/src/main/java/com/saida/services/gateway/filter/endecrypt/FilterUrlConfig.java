package com.saida.services.gateway.filter.endecrypt;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * description:
 * author zhangjc
 * date 2022/9/30 15:51
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "filter.url")
public class FilterUrlConfig {
    /**
     * 返回过滤地址
     */
    private List<String> response;
    /**
     * 请求过滤地址
     */
    private List<String> request;

    /**
     * 不校验token
     */
    private List<String> ignoreUrls;
}
