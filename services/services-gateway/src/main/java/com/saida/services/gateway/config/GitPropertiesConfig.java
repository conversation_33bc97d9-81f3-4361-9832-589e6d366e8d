package com.saida.services.gateway.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnResource;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Data
@Configuration
@ConditionalOnResource(resources = "classpath:git.properties")
@PropertySource("classpath:git.properties")
public class GitPropertiesConfig {
    @Value("${git.branch:无}")
    private String branch;

    @Value("${git.build.host:无}")
    private String buildHost;

    @Value("${git.build.time:无}")
    private String buildTime;

    @Value("${git.build.user.email:无}")
    private String buildUserEmail;

    @Value("${git.build.user.name:无}")
    private String buildUserName;

    @Value("${git.build.version:无}")
    private String buildVersion;

    @Value("${git.closest.tag.commit.count:无}")
    private String closestTagCommitCount;

    @Value("${git.closest.tag.name:无}")
    private String closestTagName;

    @Value("${git.commit.author.time:无}")
    private String commitAuthorTime;

    @Value("${git.commit.committer.time:无}")
    private String commitCommitterTime;

    @Value("${git.commit.id:无}")
    private String commitId;

    @Value("${git.commit.id.abbrev:无}")
    private String commitIdAbbrev;

    @Value("${git.commit.id.describe:无}")
    private String commitIdDescribe;

    @Value("${git.commit.id.describe-short:无}")
    private String commitIdDescribeShort;

    @Value("${git.commit.message.full:无}")
    private String commitMessageFull;

    @Value("${git.commit.message.short:无}")
    private String commitMessageShort;

    @Value("${git.commit.time:无}")
    private String commitTime;

    @Value("${git.commit.user.email:无}")
    private String commitUserEmail;

    @Value("${git.commit.user.name:无}")
    private String commitUserName;

    @Value("${git.dirty:无}")
    private String dirty;

    @Value("${git.local.branch.ahead:无}")
    private String localBranchAhead;

    @Value("${git.local.branch.behind:无}")
    private String localBranchBehind;

    @Value("${git.remote.origin.url:无}")
    private String remoteOriginUrl;

    @Value("${git.tags:无}")
    private String tags;

    @Value("${git.total.commit.count:无}")
    private String totalCommitCount;

}
