package com.saida.services.gateway.result;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
public enum ResultCode implements IResultCode, Serializable {

    SUCCESS(200, "成功"),

    TOKEN_INVALID_OR_EXPIRED(1401, "token无效"),

    TOKEN_EXPIRED(1402, "token过期"),

    TOKEN_ACCESS_FORBIDDEN(10231, "token已被禁止访问"),

    ACCESS_UNAUTHORIZED(10301, "访问未授权"),

    SYSTEM_EXECUTION_ERROR(10001, "系统执行出错"),

    BAD_REQUEST_PARAM(4000, "参数错误"),
    /**
     * 时间错误
     */
    TIME_ERROR(4501, "时间错误"),
    /**
     * 时间不同步
     */
    TIME_SYNC_ERROR(4502, "时间不同步"),
    /**
     * 签名错误
     */
    SIGN_ERROR(4503, "签名错误"),
    /**
     * 认证信息错误
     */
    AUTHENTICATION_ERROR(4504, "认证信息错误"),
    /**
     * 应用信息错误
     */
    APP_ERROR(4505, "应用信息错误"),

    APP_STATUS_ERROR(4505, "应用信息状态错误"),

    /**
     * 能力错误
     */
    CAPABILITY_ERROR(4506, "能力错误"),
    /**
     * 无权限
     */
    ACCESS_DENIED(4507, "无权限"),
    /**
     * 订单无效
     */
    ORDER_INVALID(4508, "订单无效"),
    /**
     * 订单过期
     */
    ORDER_EXPIRE(4509, "订单过期"),
    /**
     * 调用次数已达上限
     */
    CALL_UPPER_LIMIT(4510, "调用次数已达上限");

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    private Integer code;

    private String msg;

    @Override
    public String toString() {
        return "{" +
               "\"code\":\"" + code + '\"' +
               ", \"msg\":\"" + msg + '\"' +
               '}';
    }

}
