package com.saida.services.gateway.filter.endecrypt;


import lombok.Getter;
import org.springframework.web.server.ServerWebExchange;

import java.util.Map;

/**
 * 网关上下文
 */
@Getter
public class GatewayContext {

    private GatewayContext(){}

    public static final String CACHE_GATEWAY_CONTEXT = "CacheGatewayContext";

    /**
     * cache param
     */
    private Map<String, Object> param;

    private boolean ignoreDecode = false;

    private byte[] reqData;

    private Long userId;

    private String account;

    private String userName;

    public void setParam(Map<String, Object> param) {
        this.param = param;
    }

    public void setIgnoreDecode(boolean ignoreDecode) {
        this.ignoreDecode = ignoreDecode;
    }

    public void setReqData(byte[] reqData) {
        this.reqData = reqData;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public static GatewayContext getGatewayContext(ServerWebExchange exchange){
        GatewayContext gatewayContext = exchange.getAttribute(GatewayContext.CACHE_GATEWAY_CONTEXT);
        if(gatewayContext != null){
            return gatewayContext;
        }
        gatewayContext = new GatewayContext();
        exchange.getAttributes().put(GatewayContext.CACHE_GATEWAY_CONTEXT, gatewayContext);
        return gatewayContext;
    }
}

