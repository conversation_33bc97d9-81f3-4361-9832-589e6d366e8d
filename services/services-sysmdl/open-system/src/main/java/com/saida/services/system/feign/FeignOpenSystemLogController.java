package com.saida.services.system.feign;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.dto.JumpDto;
import com.saida.services.common.dto.LoginToAdminByUser;
import com.saida.services.feign.system.IFeignOpenSystemLogController;
import com.saida.services.system.pojo.Token;
import com.saida.services.system.auth.service.AuthService;
import com.saida.services.system.sys.entity.AppCallLogEntity;
import com.saida.services.system.sys.entity.OperatorLogEntity;
import com.saida.services.system.sys.service.AppCallLogService;
import com.saida.services.system.sys.service.OperatorLogService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
public class FeignOpenSystemLogController implements IFeignOpenSystemLogController {

    @Resource
    private OperatorLogService operatorLogService;

    @Resource
    private AppCallLogService appCallLogService;

    @Override
    public void saveOperatorLog(OperatorLogEntity entity) {
        operatorLogService.saveOperatorLog(entity);
    }

    @Override
    public void saveAppCallLog(AppCallLogEntity entity) {
        appCallLogService.saveAppCallLog(entity);
    }


    @Resource
    private AuthService authService;
    @Override
    public DtoResult<Token> loginToAdmin() {
        return authService.getTokenByAdmin();
    }

    @Override
    public DtoResult<JumpDto> loginToAdminByUser(LoginToAdminByUser loginToAdminByUser) {
        loginToAdminByUser.setPid(4);
        return authService.loginToAdminByUser(loginToAdminByUser);
    }
}
