project:
  name: V-LINKER-汇聚
  version: 1.0.0
  version-time: 2024-04-28
feign:
  sentinel:
    enabled: true  # 启用 Feign 与 Sentinel 集成
  client:
    config:
      default:
        logger-level: full
        connectTimeout: 30000
        readTimeout: 30000
server:
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 100
    threads:
      # tomcat最大线程数，默认为200
      max: 200
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 10
  max-http-header-size: 32KB
logging:
  level:
    # debug模式下打印所有日志
    org.springframework.boot.context: DEBUG
    com.saida.services.system: debug
    com.alibaba.nacos.client.config.impl: WARN
    springfox.documentation.spring.web.readers.operation: WARN
    com.baomidou.mybatisplus.core.metadata: WARN
    org.apache.rocketmq: OFF  # 关闭mq的日志 不然直接打到本地
    com.baomidou.mybatisplus: ERROR
    com.baomidou.mybatisplus.core: ERROR
    org.apache.kafka: ERROR
  config: classpath:logback-spring.xml
management:
  tracing:
    enabled: true
  health:
    refresh:
      enabled: false
  endpoint:
    pause:
      enabled: false
    refresh:
      enabled: false
    restart:
      enabled: false
    resume:
      enabled: false
    env:
      post:
        enabled: false

spring:
  main:
    allow-circular-references: true
  # 数据源配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      # 超时时间 单位毫秒 这样会让服务挂掉 能让外部的程序从新拉起连接 还能从新走初始化逻辑
      initialization-fail-timeout: 10000
mybatis-plus:
  configuration:
    ## mybatis不输出sql语句
#    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
sdk:
  dahua:
    enable: false

# 1400 服务
one-four-zero:
  enable: false

ju-long-mqtt:
  enable: false


tts:
  enable: false
  url: http://127.0.0.1:8000
  voices:
    - code: 1
      name: 帅哥1
      voices: zm_yunxi
    - code: 2
      name: 美女1
      voices: zf_xiaobei
    - code: 3
      name: 帅哥2
      voices: zm_yunjian
