<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <!-- 开启jmx功能来实现动态修改日志级别 -->
    <jmxConfigurator/>

    <springProperty scope="context" name="applicationName" source="spring.application.name"
                    defaultValue="converge-system-server"/>


    <!--定义日志文件的存储地址和前缀名 -->
    <property name="LOG_HOME" value="logs/${applicationName}/console"/>
    <property name="LOG_PREFIX" value="services"/>
    <!-- You can override this to have a custom pattern -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
    <property name="FILE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [${applicationName},%X{traceId},%X{spanId}] [%-20thread] %-5level %logger{36} -%msg%n"/>

    <!-- 一般信息按照每天生成日志文件 -->
    <appender name="INFO_FILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/${LOG_PREFIX}-info.log</File>
        <rollingPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 每天一归档 -->
            <fileNamePattern>${LOG_HOME}/${LOG_PREFIX}-info-%d{yyyyMMdd}.log.%i
            </fileNamePattern>
            <!-- 单个日志文件最多500MB, 30天的日志周期，最大不能超过20GB -->
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <!--格式化输出：%d表示日期，%-20thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
            <Pattern>${FILE_LOG_PATTERN}</Pattern>
        </encoder>
    </appender>

    <!--错误信息按照每天生成日志文件 -->
    <appender name="ERROR_FILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <File>${LOG_HOME}/${LOG_PREFIX}-error.log</File>
        <rollingPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 每天一归档 -->
            <fileNamePattern>${LOG_HOME}/${LOG_PREFIX}-error-%d{yyyyMMdd}.log.%i</fileNamePattern>
            <!-- 单个日志文件最多500MB, 30天的日志周期，最大不能超过20GB -->
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <!--格式化输出：%d表示日期，%-20thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
            <Pattern>${FILE_LOG_PATTERN}</Pattern>
        </encoder>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <!--格式化输出：%d表示日期，%-20thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 ：
            |%blue(%-20thread)  线程 如 ：DiscoveryClient-CacheRefreshExecutor-0-->
            <!--<pattern>%yellow(%date{yyyy-MM-dd HH:mm:ss}) |%highlight(%-5level)  |%green(%logger:%line) |%black(%msg%n)</pattern>-->
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <!--设置编码-->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- rocketmq日志 -->
    <appender name="RocketmqClientAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/rocketmqlogs/rocketmq_client.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/history/rocketmq_client.%d{yyyyMMdd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>104857600</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!--保留时间,单位:天-->
            <maxHistory>3</maxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>%d{yy-MM-dd.HH:mm:ss.SSS} [%-16t] %-5p %-22c{0} %X{ServiceId} - %m%n</pattern>
        </encoder>
    </appender>


    <logger name="RocketmqClient" additivity="false">
        <level value="warn"/>
        <appender-ref ref="RocketmqClientAppender"/>
    </logger>

    <appender name="XXL" class="com.saida.services.common.config.xxl.XxlJobLogbackAppender" />

    <!-- 日志输出级别 这样设置不打印日志 -->
    <root level="INFO">
        <appender-ref ref="XXL"/>
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>
</configuration>
